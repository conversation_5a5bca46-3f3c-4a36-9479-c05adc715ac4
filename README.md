# 💕 爱情个人软件 (Love Personal Software)

一个基于Go语言开发的桌面端爱情管理软件，帮助用户记录和管理恋爱生活中的美好时光。

## 🎯 项目概述

这是一个专为情侣设计的个人软件，提供恋爱日记、纪念日管理、照片收藏、情话记录等功能，所有数据支持本地存储和Redis数据库存储两种方案。

## ✨ 核心功能

### 1. 恋爱日记 📝
- 每日心情记录
- 重要事件标记
- 情感状态跟踪
- 文字和图片混合记录

### 2. 纪念日管理 🎉
- 重要日期提醒
- 倒计时功能
- 自定义纪念日类型
- 历史纪念日回顾

### 3. 照片收藏 📸
- 情侣照片管理
- 按时间/事件分类
- 照片标签和备注
- 幻灯片播放

### 4. 情话收藏 💌
- 甜蜜话语记录
- 分类管理（表白、日常、节日等）
- 随机显示功能
- 导出分享功能

### 5. 恋爱统计 📊
- 恋爱天数统计
- 活动频率分析
- 情感趋势图表
- 年度总结报告

## 🛠 技术栈

- **前端UI**: Fyne (Go原生GUI框架)
- **后端逻辑**: Go 1.21+
- **数据存储**: 
  - 方案一：本地JSON文件 + SQLite
  - 方案二：Redis数据库
- **图片处理**: Go标准库 + 第三方图像库
- **打包工具**: Go build + UPX压缩

## 📁 项目结构

```
love-software/
├── cmd/                    # 应用程序入口
│   └── main.go
├── internal/              # 内部包
│   ├── ui/               # UI界面
│   ├── service/          # 业务逻辑
│   ├── storage/          # 数据存储
│   └── model/            # 数据模型
├── assets/               # 静态资源
│   ├── icons/           # 图标文件
│   └── images/          # 默认图片
├── data/                # 本地数据目录
├── config/              # 配置文件
├── docs/                # 文档目录
├── scripts/             # 构建脚本
├── go.mod
├── go.sum
└── README.md
```

## 🚀 快速开始

### 环境要求
- Go 1.21 或更高版本
- Git
- (可选) Redis 服务器

### 安装步骤
```bash
# 克隆项目
git clone <repository-url>
cd love-software

# 安装依赖
go mod tidy

# 运行程序
go run cmd/main.go

# 构建可执行文件
go build -o love-software.exe cmd/main.go
```

## 📋 开发进度

- [x] 项目架构设计
- [ ] UI界面设计
- [ ] 核心功能开发
- [ ] 数据存储实现
- [ ] 测试与优化
- [ ] 文档完善

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

---

💝 用心记录每一个美好瞬间，让爱情更加甜蜜！
