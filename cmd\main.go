package main

import (
	"log"

	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"love-software/internal/ui"
	"love-software/internal/service"
	"love-software/internal/storage"
)

func main() {
	// 创建应用程序
	myApp := app.NewWithID("com.love.software")
	myApp.SetIcon(theme.FavoriteIcon())
	
	// 创建主窗口
	myWindow := myApp.NewWindow("💕 爱情个人软件")
	myWindow.Resize(fyne.NewSize(1200, 800))
	myWindow.CenterOnScreen()
	
	// 初始化存储层
	storageManager, err := storage.NewLocalStorage("./data")
	if err != nil {
		log.Fatalf("初始化存储失败: %v", err)
	}
	
	// 初始化服务层
	services := service.NewServiceManager(storageManager)
	
	// 创建UI管理器
	uiManager := ui.NewUIManager(myWindow, services)
	
	// 设置主界面内容
	content := createMainContent(uiManager)
	myWindow.SetContent(content)
	
	// 显示窗口并运行
	myWindow.ShowAndRun()
}

// createMainContent 创建主界面内容
func createMainContent(uiManager *ui.UIManager) *container.Border {
	// 创建顶部工具栏
	toolbar := container.NewHBox(
		widget.NewLabel("💕 爱情个人软件"),
		widget.NewSeparator(),
		widget.NewButton("设置", func() {
			// TODO: 打开设置界面
		}),
	)
	
	// 创建左侧导航菜单
	navigation := uiManager.CreateNavigationMenu()
	
	// 创建主内容区域
	mainContent := uiManager.CreateMainContent()
	
	// 创建状态栏
	statusBar := container.NewHBox(
		widget.NewLabel("就绪"),
		widget.NewSeparator(),
		widget.NewLabel("💝 记录每一个美好瞬间"),
	)
	
	// 组合界面
	return container.NewBorder(
		toolbar,    // 顶部
		statusBar,  // 底部
		navigation, // 左侧
		nil,        // 右侧
		mainContent, // 中心
	)
}
