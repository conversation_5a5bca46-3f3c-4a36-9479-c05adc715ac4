package service

import (
	"fmt"
	"time"

	"github.com/google/uuid"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// diaryService 日记服务实现
type diaryService struct {
	storage storage.Storage
}

// NewDiaryService 创建日记服务
func NewDiaryService(storage storage.Storage) DiaryService {
	return &diaryService{
		storage: storage,
	}
}

// CreateEntry 创建日记条目
func (s *diaryService) CreateEntry(entry *model.DiaryEntry) error {
	// 验证输入
	if err := s.validateEntry(entry); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	// 设置ID和时间戳
	entry.ID = uuid.New().String()
	entry.CreatedAt = time.Now()
	entry.UpdatedAt = time.Now()
	
	// 保存到存储
	if err := s.storage.SaveDiaryEntry(entry); err != nil {
		return fmt.Errorf("保存日记失败: %w", err)
	}
	
	return nil
}

// GetEntry 获取单个日记条目
func (s *diaryService) GetEntry(id string) (*model.DiaryEntry, error) {
	if id == "" {
		return nil, fmt.Errorf("日记ID不能为空")
	}
	
	entry, err := s.storage.GetDiaryEntry(id)
	if err != nil {
		return nil, fmt.Errorf("获取日记失败: %w", err)
	}
	
	return entry, nil
}

// GetEntries 获取日记条目列表
func (s *diaryService) GetEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error) {
	entries, err := s.storage.GetDiaryEntries(filter)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	// 按日期倒序排列
	for i, j := 0, len(entries)-1; i < j; i, j = i+1, j-1 {
		entries[i], entries[j] = entries[j], entries[i]
	}
	
	return entries, nil
}

// UpdateEntry 更新日记条目
func (s *diaryService) UpdateEntry(entry *model.DiaryEntry) error {
	// 验证输入
	if err := s.validateEntry(entry); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	if entry.ID == "" {
		return fmt.Errorf("日记ID不能为空")
	}
	
	// 检查日记是否存在
	existing, err := s.storage.GetDiaryEntry(entry.ID)
	if err != nil {
		return fmt.Errorf("日记不存在: %w", err)
	}
	
	// 保留创建时间，更新修改时间
	entry.CreatedAt = existing.CreatedAt
	entry.UpdatedAt = time.Now()
	
	// 更新存储
	if err := s.storage.UpdateDiaryEntry(entry); err != nil {
		return fmt.Errorf("更新日记失败: %w", err)
	}
	
	return nil
}

// DeleteEntry 删除日记条目
func (s *diaryService) DeleteEntry(id string) error {
	if id == "" {
		return fmt.Errorf("日记ID不能为空")
	}
	
	// 检查日记是否存在
	_, err := s.storage.GetDiaryEntry(id)
	if err != nil {
		return fmt.Errorf("日记不存在: %w", err)
	}
	
	// 删除日记
	if err := s.storage.DeleteDiaryEntry(id); err != nil {
		return fmt.Errorf("删除日记失败: %w", err)
	}
	
	return nil
}

// SearchEntries 搜索日记条目
func (s *diaryService) SearchEntries(keyword string) ([]*model.DiaryEntry, error) {
	if keyword == "" {
		return nil, fmt.Errorf("搜索关键词不能为空")
	}
	
	// 使用筛选器进行搜索
	filter := &model.DiaryFilter{
		Keyword: keyword,
	}
	
	return s.GetEntries(filter)
}

// validateEntry 验证日记条目
func (s *diaryService) validateEntry(entry *model.DiaryEntry) error {
	if entry == nil {
		return fmt.Errorf("日记条目不能为空")
	}
	
	if entry.Title == "" {
		return fmt.Errorf("日记标题不能为空")
	}
	
	if entry.Content == "" {
		return fmt.Errorf("日记内容不能为空")
	}
	
	if entry.Mood < 1 || entry.Mood > 5 {
		return fmt.Errorf("心情评分必须在1-5之间")
	}
	
	if entry.Date.IsZero() {
		entry.Date = time.Now()
	}
	
	return nil
}
