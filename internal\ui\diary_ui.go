package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"love-software/internal/service"
)

// DiaryUI 日记界面
type DiaryUI struct {
	service service.DiaryService
	
	// UI组件
	entryList   *widget.List
	entryEditor *container.VBox
	currentView string // "list" 或 "editor"
}

// NewDiaryUI 创建日记界面
func NewDiaryUI(service service.DiaryService) *DiaryUI {
	return &DiaryUI{
		service: service,
	}
}

// CreateView 创建日记视图
func (ui *DiaryUI) CreateView() *container.VBox {
	// 标题栏
	title := widget.NewLabelWithStyle("📝 恋爱日记", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	
	// 工具栏
	toolbar := container.NewHBox(
		widget.NewButtonWithIcon("新建日记", theme.DocumentCreateIcon(), func() {
			ui.showEditor(nil)
		}),
		widget.NewButtonWithIcon("搜索", theme.SearchIcon(), func() {
			ui.showSearchDialog()
		}),
		widget.NewButtonWithIcon("筛选", theme.ViewListIcon(), func() {
			ui.showFilterDialog()
		}),
	)
	
	// 创建日记列表
	ui.entryList = ui.createEntryList()
	
	// 创建编辑器
	ui.entryEditor = ui.createEntryEditor()
	
	// 主内容区域
	content := container.NewStack(
		ui.entryList,
		ui.entryEditor,
	)
	
	// 默认显示列表
	ui.showList()
	
	return container.NewVBox(
		title,
		widget.NewSeparator(),
		toolbar,
		content,
	)
}

// createEntryList 创建日记列表
func (ui *DiaryUI) createEntryList() *widget.List {
	// 模拟数据
	entries := []string{
		"2024-01-15 - 第一次约会 💕",
		"2024-01-20 - 一起看电影 🎬",
		"2024-01-25 - 收到的第一份礼物 🎁",
		"2024-02-01 - 情人节准备 💝",
		"2024-02-14 - 甜蜜的情人节 🌹",
	}
	
	list := widget.NewList(
		func() int {
			return len(entries)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(theme.DocumentIcon()),
				widget.NewLabel("日记标题"),
				widget.NewSeparator(),
				widget.NewLabel("日期"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			container := obj.(*container.HBox)
			label := container.Objects[1].(*widget.Label)
			dateLabel := container.Objects[3].(*widget.Label)
			
			label.SetText(entries[id])
			dateLabel.SetText("2024-01-15")
		},
	)
	
	list.OnSelected = func(id widget.ListItemID) {
		// TODO: 加载并显示选中的日记
		ui.showEditor(nil)
	}
	
	return list
}

// createEntryEditor 创建日记编辑器
func (ui *DiaryUI) createEntryEditor() *container.VBox {
	// 返回按钮
	backBtn := widget.NewButtonWithIcon("返回列表", theme.NavigateBackIcon(), func() {
		ui.showList()
	})
	
	// 标题输入
	titleEntry := widget.NewEntry()
	titleEntry.SetPlaceHolder("请输入日记标题...")
	
	// 日期选择
	dateLabel := widget.NewLabel("日期: 2024-01-15")
	
	// 心情选择
	moodSelect := widget.NewSelect([]string{"😢 难过", "😐 一般", "😊 开心", "😍 甜蜜", "🥰 超级开心"}, nil)
	moodSelect.SetSelected("😊 开心")
	
	// 内容编辑
	contentEntry := widget.NewMultiLineEntry()
	contentEntry.SetPlaceHolder("记录今天的美好时光...")
	contentEntry.Resize(fyne.NewSize(600, 300))
	
	// 标签输入
	tagsEntry := widget.NewEntry()
	tagsEntry.SetPlaceHolder("标签 (用逗号分隔)")
	
	// 重要标记
	importantCheck := widget.NewCheck("标记为重要", nil)
	
	// 图片区域
	imageArea := widget.NewCard("📸 图片", "", container.NewVBox(
		widget.NewButton("添加图片", func() {
			// TODO: 实现图片上传
		}),
		widget.NewLabel("暂无图片"),
	))
	
	// 操作按钮
	actionButtons := container.NewHBox(
		widget.NewButtonWithIcon("保存", theme.DocumentSaveIcon(), func() {
			// TODO: 保存日记
			ui.showList()
		}),
		widget.NewButtonWithIcon("删除", theme.DeleteIcon(), func() {
			// TODO: 删除日记
			ui.showList()
		}),
	)
	
	return container.NewVBox(
		backBtn,
		widget.NewSeparator(),
		widget.NewForm(
			widget.NewFormItem("标题", titleEntry),
			widget.NewFormItem("日期", dateLabel),
			widget.NewFormItem("心情", moodSelect),
			widget.NewFormItem("内容", contentEntry),
			widget.NewFormItem("标签", tagsEntry),
			widget.NewFormItem("", importantCheck),
		),
		imageArea,
		actionButtons,
	)
}

// showList 显示日记列表
func (ui *DiaryUI) showList() {
	ui.currentView = "list"
	ui.entryList.Show()
	ui.entryEditor.Hide()
}

// showEditor 显示日记编辑器
func (ui *DiaryUI) showEditor(entryID *string) {
	ui.currentView = "editor"
	ui.entryList.Hide()
	ui.entryEditor.Show()
	
	// TODO: 如果有entryID，加载对应的日记数据
}

// showSearchDialog 显示搜索对话框
func (ui *DiaryUI) showSearchDialog() {
	// TODO: 实现搜索功能
}

// showFilterDialog 显示筛选对话框
func (ui *DiaryUI) showFilterDialog() {
	// TODO: 实现筛选功能
}
