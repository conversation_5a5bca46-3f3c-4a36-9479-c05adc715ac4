package service

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/google/uuid"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// quoteService 情话服务实现
type quoteService struct {
	storage storage.Storage
}

// NewQuoteService 创建情话服务
func NewQuoteService(storage storage.Storage) QuoteService {
	return &quoteService{
		storage: storage,
	}
}

// CreateQuote 创建情话
func (s *quoteService) CreateQuote(quote *model.LoveQuote) error {
	// 验证输入
	if err := s.validateQuote(quote); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	// 设置ID和时间戳
	quote.ID = uuid.New().String()
	quote.CreatedAt = time.Now()
	
	// 保存到存储
	if err := s.storage.SaveLoveQuote(quote); err != nil {
		return fmt.Errorf("保存情话失败: %w", err)
	}
	
	return nil
}

// GetQuote 获取单个情话
func (s *quoteService) GetQuote(id string) (*model.LoveQuote, error) {
	if id == "" {
		return nil, fmt.Errorf("情话ID不能为空")
	}
	
	quote, err := s.storage.GetLoveQuote(id)
	if err != nil {
		return nil, fmt.Errorf("获取情话失败: %w", err)
	}
	
	return quote, nil
}

// GetQuotes 获取情话列表
func (s *quoteService) GetQuotes(filter *model.QuoteFilter) ([]*model.LoveQuote, error) {
	quotes, err := s.storage.GetLoveQuotes(filter)
	if err != nil {
		return nil, fmt.Errorf("获取情话列表失败: %w", err)
	}
	
	// 按创建时间倒序排列
	for i, j := 0, len(quotes)-1; i < j; i, j = i+1, j-1 {
		quotes[i], quotes[j] = quotes[j], quotes[i]
	}
	
	return quotes, nil
}

// UpdateQuote 更新情话
func (s *quoteService) UpdateQuote(quote *model.LoveQuote) error {
	// 验证输入
	if err := s.validateQuote(quote); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	if quote.ID == "" {
		return fmt.Errorf("情话ID不能为空")
	}
	
	// 检查情话是否存在
	existing, err := s.storage.GetLoveQuote(quote.ID)
	if err != nil {
		return fmt.Errorf("情话不存在: %w", err)
	}
	
	// 保留创建时间
	quote.CreatedAt = existing.CreatedAt
	
	// 更新存储
	if err := s.storage.UpdateLoveQuote(quote); err != nil {
		return fmt.Errorf("更新情话失败: %w", err)
	}
	
	return nil
}

// DeleteQuote 删除情话
func (s *quoteService) DeleteQuote(id string) error {
	if id == "" {
		return fmt.Errorf("情话ID不能为空")
	}
	
	// 检查情话是否存在
	_, err := s.storage.GetLoveQuote(id)
	if err != nil {
		return fmt.Errorf("情话不存在: %w", err)
	}
	
	// 删除情话
	if err := s.storage.DeleteLoveQuote(id); err != nil {
		return fmt.Errorf("删除情话失败: %w", err)
	}
	
	return nil
}

// GetRandomQuote 获取随机情话
func (s *quoteService) GetRandomQuote() (*model.LoveQuote, error) {
	// 获取所有情话
	quotes, err := s.storage.GetLoveQuotes(nil)
	if err != nil {
		return nil, fmt.Errorf("获取情话列表失败: %w", err)
	}
	
	if len(quotes) == 0 {
		return nil, fmt.Errorf("暂无情话")
	}
	
	// 随机选择一个
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(quotes))
	
	return quotes[randomIndex], nil
}

// GetFavoriteQuotes 获取收藏的情话
func (s *quoteService) GetFavoriteQuotes() ([]*model.LoveQuote, error) {
	filter := &model.QuoteFilter{
		IsFavorite: &[]bool{true}[0], // 创建bool指针
	}
	
	return s.GetQuotes(filter)
}

// ToggleFavorite 切换收藏状态
func (s *quoteService) ToggleFavorite(id string) error {
	quote, err := s.GetQuote(id)
	if err != nil {
		return err
	}
	
	quote.IsFavorite = !quote.IsFavorite
	
	return s.UpdateQuote(quote)
}

// GetQuotesByCategory 按分类获取情话
func (s *quoteService) GetQuotesByCategory(category string) ([]*model.LoveQuote, error) {
	filter := &model.QuoteFilter{
		Category: category,
	}
	
	return s.GetQuotes(filter)
}

// SearchQuotes 搜索情话
func (s *quoteService) SearchQuotes(keyword string) ([]*model.LoveQuote, error) {
	if keyword == "" {
		return nil, fmt.Errorf("搜索关键词不能为空")
	}
	
	filter := &model.QuoteFilter{
		Keyword: keyword,
	}
	
	return s.GetQuotes(filter)
}

// GetCategories 获取情话分类列表
func (s *quoteService) GetCategories() []string {
	return []string{
		"表白",
		"日常",
		"节日",
		"思念",
		"承诺",
		"感谢",
		"道歉",
		"鼓励",
		"浪漫",
		"甜蜜",
		"其他",
	}
}

// GetRandomQuoteByCategory 按分类获取随机情话
func (s *quoteService) GetRandomQuoteByCategory(category string) (*model.LoveQuote, error) {
	quotes, err := s.GetQuotesByCategory(category)
	if err != nil {
		return nil, err
	}
	
	if len(quotes) == 0 {
		return nil, fmt.Errorf("该分类暂无情话")
	}
	
	// 随机选择一个
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(quotes))
	
	return quotes[randomIndex], nil
}

// GetDailyQuote 获取每日情话
func (s *quoteService) GetDailyQuote() (*model.LoveQuote, error) {
	// 基于日期生成固定的随机种子，确保同一天返回相同的情话
	now := time.Now()
	seed := int64(now.Year()*10000 + int(now.Month())*100 + now.Day())
	rand.Seed(seed)
	
	quotes, err := s.storage.GetLoveQuotes(nil)
	if err != nil {
		return nil, fmt.Errorf("获取情话列表失败: %w", err)
	}
	
	if len(quotes) == 0 {
		return nil, fmt.Errorf("暂无情话")
	}
	
	randomIndex := rand.Intn(len(quotes))
	return quotes[randomIndex], nil
}

// validateQuote 验证情话
func (s *quoteService) validateQuote(quote *model.LoveQuote) error {
	if quote == nil {
		return fmt.Errorf("情话不能为空")
	}
	
	if quote.Content == "" {
		return fmt.Errorf("情话内容不能为空")
	}
	
	if quote.Category == "" {
		quote.Category = "其他" // 默认分类
	}
	
	// 验证分类是否有效
	validCategories := s.GetCategories()
	isValidCategory := false
	for _, category := range validCategories {
		if quote.Category == category {
			isValidCategory = true
			break
		}
	}
	
	if !isValidCategory {
		quote.Category = "其他"
	}
	
	return nil
}
