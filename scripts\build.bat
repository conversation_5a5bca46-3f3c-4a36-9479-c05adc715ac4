@echo off
echo 💕 爱情个人软件 - 构建脚本
echo ================================

:: 设置环境变量
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=1

:: 创建输出目录
if not exist "dist" mkdir dist

echo 📦 正在构建应用程序...

:: 构建主程序
go build -ldflags "-s -w -H windowsgui" -o dist/love-software.exe cmd/main.go

if %ERRORLEVEL% EQU 0 (
    echo ✅ 构建成功！
    echo 📁 输出文件: dist/love-software.exe
    
    :: 复制资源文件
    echo 📋 复制资源文件...
    if not exist "dist/assets" mkdir dist/assets
    if exist "assets" xcopy /E /I /Y assets dist/assets
    
    :: 创建数据目录
    if not exist "dist/data" mkdir dist/data
    
    :: 显示文件信息
    echo.
    echo 📊 文件信息:
    dir dist\love-software.exe
    
    echo.
    echo 🎉 构建完成！可以运行 dist/love-software.exe
) else (
    echo ❌ 构建失败！
    exit /b 1
)

pause
