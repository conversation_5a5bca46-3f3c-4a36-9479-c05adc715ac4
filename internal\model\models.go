package model

import (
	"time"
)

// DiaryEntry 恋爱日记条目
type DiaryEntry struct {
	ID          string    `json:"id"`
	Date        time.Time `json:"date"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Mood        int       `json:"mood"`         // 1-5心情评分
	Images      []string  `json:"images"`       // 图片路径列表
	Tags        []string  `json:"tags"`         // 标签
	IsImportant bool      `json:"is_important"` // 重要标记
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Anniversary 纪念日
type Anniversary struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Date        time.Time `json:"date"`
	Type        string    `json:"type"`         // 恋爱、结婚、生日等
	Description string    `json:"description"`
	IsRecurring bool      `json:"is_recurring"` // 是否每年重复
	RemindDays  int       `json:"remind_days"`  // 提前提醒天数
	CreatedAt   time.Time `json:"created_at"`
}

// PhotoAlbum 相册
type PhotoAlbum struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CoverImage  string    `json:"cover_image"`
	Photos      []Photo   `json:"photos"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Photo 照片
type Photo struct {
	ID        string    `json:"id"`
	FilePath  string    `json:"file_path"`
	Caption   string    `json:"caption"`
	TakenAt   time.Time `json:"taken_at"`
	Tags      []string  `json:"tags"`
	Location  string    `json:"location"`
	CreatedAt time.Time `json:"created_at"`
}

// LoveQuote 情话
type LoveQuote struct {
	ID         string    `json:"id"`
	Content    string    `json:"content"`
	Category   string    `json:"category"`   // 表白、日常、节日等
	Source     string    `json:"source"`     // 来源
	IsFavorite bool      `json:"is_favorite"`
	CreatedAt  time.Time `json:"created_at"`
}

// UserConfig 用户配置
type UserConfig struct {
	Theme           string `json:"theme"`            // 主题
	Language        string `json:"language"`         // 语言
	StorageType     string `json:"storage_type"`     // 存储类型: local/redis
	RedisConfig     *RedisConfig `json:"redis_config,omitempty"`
	BackupEnabled   bool   `json:"backup_enabled"`   // 是否启用备份
	BackupInterval  int    `json:"backup_interval"`  // 备份间隔(天)
	NotifyEnabled   bool   `json:"notify_enabled"`   // 是否启用通知
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Password string `json:"password"`
	Database int    `json:"database"`
}

// DiaryFilter 日记筛选条件
type DiaryFilter struct {
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
	Tags        []string   `json:"tags,omitempty"`
	Mood        *int       `json:"mood,omitempty"`
	IsImportant *bool      `json:"is_important,omitempty"`
	Keyword     string     `json:"keyword,omitempty"`
}

// AnniversaryFilter 纪念日筛选条件
type AnniversaryFilter struct {
	Type        string     `json:"type,omitempty"`
	StartDate   *time.Time `json:"start_date,omitempty"`
	EndDate     *time.Time `json:"end_date,omitempty"`
	IsRecurring *bool      `json:"is_recurring,omitempty"`
}

// PhotoFilter 照片筛选条件
type PhotoFilter struct {
	AlbumID   string     `json:"album_id,omitempty"`
	Tags      []string   `json:"tags,omitempty"`
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	Location  string     `json:"location,omitempty"`
}

// QuoteFilter 情话筛选条件
type QuoteFilter struct {
	Category   string `json:"category,omitempty"`
	IsFavorite *bool  `json:"is_favorite,omitempty"`
	Keyword    string `json:"keyword,omitempty"`
}

// Statistics 统计信息
type Statistics struct {
	TotalDiaryEntries int            `json:"total_diary_entries"`
	TotalPhotos       int            `json:"total_photos"`
	TotalQuotes       int            `json:"total_quotes"`
	TotalAnniversaries int           `json:"total_anniversaries"`
	LoveDays          int            `json:"love_days"`          // 恋爱天数
	MoodDistribution  map[int]int    `json:"mood_distribution"`  // 心情分布
	MonthlyActivity   map[string]int `json:"monthly_activity"`   // 月度活跃度
}

// Notification 通知
type Notification struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`      // anniversary, reminder等
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	IsRead    bool      `json:"is_read"`
	CreatedAt time.Time `json:"created_at"`
}

// BackupInfo 备份信息
type BackupInfo struct {
	ID        string    `json:"id"`
	FileName  string    `json:"file_name"`
	Size      int64     `json:"size"`
	CreatedAt time.Time `json:"created_at"`
}
