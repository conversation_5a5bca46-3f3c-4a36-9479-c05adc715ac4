package service

import (
	"fmt"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// anniversaryService 已移至 anniversary_service.go

// photoService 已移至 photo_service.go

// quoteService 已移至 quote_service.go

// statisticsService 已移至 statistics_service.go

// notificationService 通知服务占位符实现
type notificationService struct {
	storage storage.Storage
}

func NewNotificationService(storage storage.Storage) NotificationService {
	return &notificationService{storage: storage}
}

func (s *notificationService) CreateNotification(notification *model.Notification) error {
	return fmt.Errorf("通知服务功能开发中")
}

func (s *notificationService) GetNotifications(unreadOnly bool) ([]*model.Notification, error) {
	return nil, fmt.Errorf("通知服务功能开发中")
}

func (s *notificationService) MarkAsRead(id string) error {
	return fmt.Errorf("通知服务功能开发中")
}

func (s *notificationService) DeleteNotification(id string) error {
	return fmt.Errorf("通知服务功能开发中")
}

func (s *notificationService) CheckAnniversaryReminders() error {
	return fmt.Errorf("通知服务功能开发中")
}

// backupService 备份服务占位符实现
type backupService struct {
	storage storage.Storage
}

func NewBackupService(storage storage.Storage) BackupService {
	return &backupService{storage: storage}
}

func (s *backupService) CreateBackup() (*model.BackupInfo, error) {
	return nil, fmt.Errorf("备份服务功能开发中")
}

func (s *backupService) RestoreBackup(backupID string) error {
	return fmt.Errorf("备份服务功能开发中")
}

func (s *backupService) GetBackups() ([]*model.BackupInfo, error) {
	return nil, fmt.Errorf("备份服务功能开发中")
}

func (s *backupService) DeleteBackup(backupID string) error {
	return fmt.Errorf("备份服务功能开发中")
}

func (s *backupService) AutoBackup() error {
	return fmt.Errorf("备份服务功能开发中")
}
