package service

import (
	"fmt"
	"sort"
	"time"

	"github.com/google/uuid"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// anniversaryService 纪念日服务实现
type anniversaryService struct {
	storage storage.Storage
}

// NewAnniversaryService 创建纪念日服务
func NewAnniversaryService(storage storage.Storage) AnniversaryService {
	return &anniversaryService{
		storage: storage,
	}
}

// CreateAnniversary 创建纪念日
func (s *anniversaryService) CreateAnniversary(anniversary *model.Anniversary) error {
	// 验证输入
	if err := s.validateAnniversary(anniversary); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	// 设置ID和时间戳
	anniversary.ID = uuid.New().String()
	anniversary.CreatedAt = time.Now()
	
	// 保存到存储
	if err := s.storage.SaveAnniversary(anniversary); err != nil {
		return fmt.Errorf("保存纪念日失败: %w", err)
	}
	
	return nil
}

// GetAnniversary 获取单个纪念日
func (s *anniversaryService) GetAnniversary(id string) (*model.Anniversary, error) {
	if id == "" {
		return nil, fmt.Errorf("纪念日ID不能为空")
	}
	
	anniversary, err := s.storage.GetAnniversary(id)
	if err != nil {
		return nil, fmt.Errorf("获取纪念日失败: %w", err)
	}
	
	return anniversary, nil
}

// GetAnniversaries 获取纪念日列表
func (s *anniversaryService) GetAnniversaries(filter *model.AnniversaryFilter) ([]*model.Anniversary, error) {
	anniversaries, err := s.storage.GetAnniversaries(filter)
	if err != nil {
		return nil, fmt.Errorf("获取纪念日列表失败: %w", err)
	}
	
	// 按日期排序
	sort.Slice(anniversaries, func(i, j int) bool {
		return anniversaries[i].Date.Before(anniversaries[j].Date)
	})
	
	return anniversaries, nil
}

// UpdateAnniversary 更新纪念日
func (s *anniversaryService) UpdateAnniversary(anniversary *model.Anniversary) error {
	// 验证输入
	if err := s.validateAnniversary(anniversary); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	if anniversary.ID == "" {
		return fmt.Errorf("纪念日ID不能为空")
	}
	
	// 检查纪念日是否存在
	_, err := s.storage.GetAnniversary(anniversary.ID)
	if err != nil {
		return fmt.Errorf("纪念日不存在: %w", err)
	}
	
	// 更新存储
	if err := s.storage.UpdateAnniversary(anniversary); err != nil {
		return fmt.Errorf("更新纪念日失败: %w", err)
	}
	
	return nil
}

// DeleteAnniversary 删除纪念日
func (s *anniversaryService) DeleteAnniversary(id string) error {
	if id == "" {
		return fmt.Errorf("纪念日ID不能为空")
	}
	
	// 检查纪念日是否存在
	_, err := s.storage.GetAnniversary(id)
	if err != nil {
		return fmt.Errorf("纪念日不存在: %w", err)
	}
	
	// 删除纪念日
	if err := s.storage.DeleteAnniversary(id); err != nil {
		return fmt.Errorf("删除纪念日失败: %w", err)
	}
	
	return nil
}

// GetUpcomingAnniversaries 获取即将到来的纪念日
func (s *anniversaryService) GetUpcomingAnniversaries(days int) ([]*model.Anniversary, error) {
	if days <= 0 {
		days = 30 // 默认30天
	}
	
	// 获取所有纪念日
	anniversaries, err := s.storage.GetAnniversaries(nil)
	if err != nil {
		return nil, fmt.Errorf("获取纪念日列表失败: %w", err)
	}
	
	now := time.Now()
	endDate := now.AddDate(0, 0, days)
	
	var upcoming []*model.Anniversary
	
	for _, anniversary := range anniversaries {
		nextDate := s.getNextAnniversaryDate(anniversary, now)
		
		// 检查是否在指定天数内
		if nextDate.After(now) && nextDate.Before(endDate) {
			// 创建一个副本，设置下次日期
			upcomingAnniversary := *anniversary
			upcomingAnniversary.Date = nextDate
			upcoming = append(upcoming, &upcomingAnniversary)
		}
	}
	
	// 按日期排序
	sort.Slice(upcoming, func(i, j int) bool {
		return upcoming[i].Date.Before(upcoming[j].Date)
	})
	
	return upcoming, nil
}

// getNextAnniversaryDate 获取下次纪念日日期
func (s *anniversaryService) getNextAnniversaryDate(anniversary *model.Anniversary, from time.Time) time.Time {
	if !anniversary.IsRecurring {
		return anniversary.Date
	}
	
	// 对于重复的纪念日，计算下次日期
	year := from.Year()
	nextDate := time.Date(year, anniversary.Date.Month(), anniversary.Date.Day(), 
		anniversary.Date.Hour(), anniversary.Date.Minute(), anniversary.Date.Second(), 
		anniversary.Date.Nanosecond(), anniversary.Date.Location())
	
	// 如果今年的日期已过，则计算明年的
	if nextDate.Before(from) || nextDate.Equal(from) {
		nextDate = nextDate.AddDate(1, 0, 0)
	}
	
	return nextDate
}

// validateAnniversary 验证纪念日
func (s *anniversaryService) validateAnniversary(anniversary *model.Anniversary) error {
	if anniversary == nil {
		return fmt.Errorf("纪念日不能为空")
	}
	
	if anniversary.Name == "" {
		return fmt.Errorf("纪念日名称不能为空")
	}
	
	if anniversary.Date.IsZero() {
		return fmt.Errorf("纪念日日期不能为空")
	}
	
	if anniversary.Type == "" {
		anniversary.Type = "其他" // 默认类型
	}
	
	if anniversary.RemindDays < 0 {
		anniversary.RemindDays = 0
	}
	
	return nil
}

// GetAnniversaryTypes 获取纪念日类型列表
func (s *anniversaryService) GetAnniversaryTypes() []string {
	return []string{
		"恋爱纪念日",
		"结婚纪念日",
		"生日",
		"第一次约会",
		"第一次牵手",
		"第一次接吻",
		"求婚纪念日",
		"订婚纪念日",
		"节日纪念",
		"旅行纪念",
		"其他",
	}
}

// CalculateDaysUntil 计算距离纪念日的天数
func (s *anniversaryService) CalculateDaysUntil(anniversary *model.Anniversary) int {
	now := time.Now()
	nextDate := s.getNextAnniversaryDate(anniversary, now)
	
	duration := nextDate.Sub(now)
	days := int(duration.Hours() / 24)
	
	return days
}
