package service

import (
	"fmt"
	"time"

	"github.com/google/uuid"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// photoService 照片服务实现
type photoService struct {
	storage storage.Storage
}

// NewPhotoService 创建照片服务
func NewPhotoService(storage storage.Storage) PhotoService {
	return &photoService{
		storage: storage,
	}
}

// CreateAlbum 创建相册
func (s *photoService) CreateAlbum(album *model.PhotoAlbum) error {
	// 验证输入
	if err := s.validateAlbum(album); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	// 设置ID和时间戳
	album.ID = uuid.New().String()
	album.CreatedAt = time.Now()
	album.UpdatedAt = time.Now()
	
	// 保存到存储
	if err := s.storage.SavePhotoAlbum(album); err != nil {
		return fmt.Errorf("保存相册失败: %w", err)
	}
	
	return nil
}

// GetAlbum 获取相册
func (s *photoService) GetAlbum(id string) (*model.PhotoAlbum, error) {
	if id == "" {
		return nil, fmt.Errorf("相册ID不能为空")
	}
	
	album, err := s.storage.GetPhotoAlbum(id)
	if err != nil {
		return nil, fmt.Errorf("获取相册失败: %w", err)
	}
	
	return album, nil
}

// GetAlbums 获取相册列表
func (s *photoService) GetAlbums() ([]*model.PhotoAlbum, error) {
	albums, err := s.storage.GetPhotoAlbums()
	if err != nil {
		return nil, fmt.Errorf("获取相册列表失败: %w", err)
	}
	
	// 按创建时间倒序排列
	for i, j := 0, len(albums)-1; i < j; i, j = i+1, j-1 {
		albums[i], albums[j] = albums[j], albums[i]
	}
	
	return albums, nil
}

// UpdateAlbum 更新相册
func (s *photoService) UpdateAlbum(album *model.PhotoAlbum) error {
	// 验证输入
	if err := s.validateAlbum(album); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	if album.ID == "" {
		return fmt.Errorf("相册ID不能为空")
	}
	
	// 检查相册是否存在
	existing, err := s.storage.GetPhotoAlbum(album.ID)
	if err != nil {
		return fmt.Errorf("相册不存在: %w", err)
	}
	
	// 保留创建时间，更新修改时间
	album.CreatedAt = existing.CreatedAt
	album.UpdatedAt = time.Now()
	
	// 更新存储
	if err := s.storage.UpdatePhotoAlbum(album); err != nil {
		return fmt.Errorf("更新相册失败: %w", err)
	}
	
	return nil
}

// DeleteAlbum 删除相册
func (s *photoService) DeleteAlbum(id string) error {
	if id == "" {
		return fmt.Errorf("相册ID不能为空")
	}
	
	// 检查相册是否存在
	album, err := s.storage.GetPhotoAlbum(id)
	if err != nil {
		return fmt.Errorf("相册不存在: %w", err)
	}
	
	// 删除相册中的所有照片
	for _, photo := range album.Photos {
		if err := s.storage.DeletePhoto(photo.ID); err != nil {
			// 记录错误但继续删除
			fmt.Printf("删除照片失败: %v\n", err)
		}
	}
	
	// 删除相册
	if err := s.storage.DeletePhotoAlbum(id); err != nil {
		return fmt.Errorf("删除相册失败: %w", err)
	}
	
	return nil
}

// AddPhoto 添加照片到相册
func (s *photoService) AddPhoto(albumID string, photo *model.Photo) error {
	// 验证输入
	if err := s.validatePhoto(photo); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	// 检查相册是否存在
	album, err := s.storage.GetPhotoAlbum(albumID)
	if err != nil {
		return fmt.Errorf("相册不存在: %w", err)
	}
	
	// 设置照片ID和时间戳
	photo.ID = uuid.New().String()
	photo.CreatedAt = time.Now()
	
	// 保存照片
	if err := s.storage.SavePhoto(photo); err != nil {
		return fmt.Errorf("保存照片失败: %w", err)
	}
	
	// 更新相册
	album.Photos = append(album.Photos, *photo)
	album.UpdatedAt = time.Now()
	
	// 如果是第一张照片，设为封面
	if len(album.Photos) == 1 {
		album.CoverImage = photo.FilePath
	}
	
	if err := s.storage.UpdatePhotoAlbum(album); err != nil {
		return fmt.Errorf("更新相册失败: %w", err)
	}
	
	return nil
}

// GetPhoto 获取照片
func (s *photoService) GetPhoto(id string) (*model.Photo, error) {
	if id == "" {
		return nil, fmt.Errorf("照片ID不能为空")
	}
	
	photo, err := s.storage.GetPhoto(id)
	if err != nil {
		return nil, fmt.Errorf("获取照片失败: %w", err)
	}
	
	return photo, nil
}

// GetPhotos 获取照片列表
func (s *photoService) GetPhotos(filter *model.PhotoFilter) ([]*model.Photo, error) {
	photos, err := s.storage.GetPhotos(filter)
	if err != nil {
		return nil, fmt.Errorf("获取照片列表失败: %w", err)
	}
	
	// 按拍摄时间倒序排列
	for i, j := 0, len(photos)-1; i < j; i, j = i+1, j-1 {
		if photos[i].TakenAt.After(photos[j].TakenAt) {
			photos[i], photos[j] = photos[j], photos[i]
		}
	}
	
	return photos, nil
}

// UpdatePhoto 更新照片信息
func (s *photoService) UpdatePhoto(photo *model.Photo) error {
	// 验证输入
	if err := s.validatePhoto(photo); err != nil {
		return fmt.Errorf("验证失败: %w", err)
	}
	
	if photo.ID == "" {
		return fmt.Errorf("照片ID不能为空")
	}
	
	// 检查照片是否存在
	existing, err := s.storage.GetPhoto(photo.ID)
	if err != nil {
		return fmt.Errorf("照片不存在: %w", err)
	}
	
	// 保留创建时间
	photo.CreatedAt = existing.CreatedAt
	
	// 更新存储
	if err := s.storage.UpdatePhoto(photo); err != nil {
		return fmt.Errorf("更新照片失败: %w", err)
	}
	
	return nil
}

// DeletePhoto 删除照片
func (s *photoService) DeletePhoto(id string) error {
	if id == "" {
		return fmt.Errorf("照片ID不能为空")
	}
	
	// 检查照片是否存在
	_, err := s.storage.GetPhoto(id)
	if err != nil {
		return fmt.Errorf("照片不存在: %w", err)
	}
	
	// 删除照片
	if err := s.storage.DeletePhoto(id); err != nil {
		return fmt.Errorf("删除照片失败: %w", err)
	}
	
	return nil
}

// validateAlbum 验证相册
func (s *photoService) validateAlbum(album *model.PhotoAlbum) error {
	if album == nil {
		return fmt.Errorf("相册不能为空")
	}
	
	if album.Name == "" {
		return fmt.Errorf("相册名称不能为空")
	}
	
	return nil
}

// validatePhoto 验证照片
func (s *photoService) validatePhoto(photo *model.Photo) error {
	if photo == nil {
		return fmt.Errorf("照片不能为空")
	}
	
	if photo.FilePath == "" {
		return fmt.Errorf("照片文件路径不能为空")
	}
	
	if photo.TakenAt.IsZero() {
		photo.TakenAt = time.Now()
	}
	
	return nil
}

// GetPhotosByAlbum 获取指定相册的照片
func (s *photoService) GetPhotosByAlbum(albumID string) ([]*model.Photo, error) {
	filter := &model.PhotoFilter{
		AlbumID: albumID,
	}
	
	return s.GetPhotos(filter)
}

// SearchPhotos 搜索照片
func (s *photoService) SearchPhotos(keyword string) ([]*model.Photo, error) {
	if keyword == "" {
		return nil, fmt.Errorf("搜索关键词不能为空")
	}
	
	// 这里简化实现，实际应该在存储层实现更复杂的搜索
	photos, err := s.storage.GetPhotos(nil)
	if err != nil {
		return nil, fmt.Errorf("获取照片列表失败: %w", err)
	}
	
	var results []*model.Photo
	for _, photo := range photos {
		if contains(photo.Caption, keyword) || contains(photo.Location, keyword) {
			results = append(results, photo)
		}
		
		// 搜索标签
		for _, tag := range photo.Tags {
			if contains(tag, keyword) {
				results = append(results, photo)
				break
			}
		}
	}
	
	return results, nil
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      containsMiddle(s, substr))))
}

func containsMiddle(s, substr string) bool {
	for i := 1; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
