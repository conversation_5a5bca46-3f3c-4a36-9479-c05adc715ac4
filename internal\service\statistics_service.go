package service

import (
	"fmt"
	"time"

	"love-software/internal/model"
	"love-software/internal/storage"
)

// statisticsService 统计服务实现
type statisticsService struct {
	storage storage.Storage
}

// NewStatisticsService 创建统计服务
func NewStatisticsService(storage storage.Storage) StatisticsService {
	return &statisticsService{
		storage: storage,
	}
}

// GetStatistics 获取统计信息
func (s *statisticsService) GetStatistics() (*model.Statistics, error) {
	stats := &model.Statistics{
		MoodDistribution: make(map[int]int),
		MonthlyActivity:  make(map[string]int),
	}
	
	// 统计日记数量
	diaryEntries, err := s.storage.GetDiaryEntries(nil)
	if err == nil {
		stats.TotalDiaryEntries = len(diaryEntries)
		
		// 统计心情分布
		for _, entry := range diaryEntries {
			stats.MoodDistribution[entry.Mood]++
		}
		
		// 统计月度活跃度
		for _, entry := range diaryEntries {
			monthKey := entry.Date.Format("2006-01")
			stats.MonthlyActivity[monthKey]++
		}
	}
	
	// 统计纪念日数量
	anniversaries, err := s.storage.GetAnniversaries(nil)
	if err == nil {
		stats.TotalAnniversaries = len(anniversaries)
	}
	
	// 统计情话数量
	quotes, err := s.storage.GetLoveQuotes(nil)
	if err == nil {
		stats.TotalQuotes = len(quotes)
	}
	
	// 统计照片数量 (暂时设为0，待实现)
	stats.TotalPhotos = 0
	
	// 计算恋爱天数
	loveDays, err := s.GetLoveDays()
	if err == nil {
		stats.LoveDays = loveDays
	}
	
	return stats, nil
}

// GetMoodDistribution 获取心情分布
func (s *statisticsService) GetMoodDistribution() (map[int]int, error) {
	diaryEntries, err := s.storage.GetDiaryEntries(nil)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	distribution := make(map[int]int)
	for _, entry := range diaryEntries {
		distribution[entry.Mood]++
	}
	
	return distribution, nil
}

// GetMonthlyActivity 获取月度活跃度
func (s *statisticsService) GetMonthlyActivity() (map[string]int, error) {
	diaryEntries, err := s.storage.GetDiaryEntries(nil)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	activity := make(map[string]int)
	for _, entry := range diaryEntries {
		monthKey := entry.Date.Format("2006-01")
		activity[monthKey]++
	}
	
	return activity, nil
}

// GetLoveDays 获取恋爱天数
func (s *statisticsService) GetLoveDays() (int, error) {
	// 查找恋爱纪念日
	filter := &model.AnniversaryFilter{
		Type: "恋爱纪念日",
	}
	
	anniversaries, err := s.storage.GetAnniversaries(filter)
	if err != nil {
		return 0, fmt.Errorf("获取纪念日失败: %w", err)
	}
	
	if len(anniversaries) == 0 {
		// 如果没有恋爱纪念日，尝试查找其他相关纪念日
		filter.Type = "第一次约会"
		anniversaries, err = s.storage.GetAnniversaries(filter)
		if err != nil || len(anniversaries) == 0 {
			return 0, nil // 没有找到相关纪念日
		}
	}
	
	// 使用最早的纪念日作为恋爱开始日期
	var startDate time.Time
	for i, anniversary := range anniversaries {
		if i == 0 || anniversary.Date.Before(startDate) {
			startDate = anniversary.Date
		}
	}
	
	// 计算天数
	now := time.Now()
	duration := now.Sub(startDate)
	days := int(duration.Hours() / 24)
	
	if days < 0 {
		days = 0
	}
	
	return days, nil
}

// GetYearlyStatistics 获取年度统计
func (s *statisticsService) GetYearlyStatistics(year int) (*model.Statistics, error) {
	if year == 0 {
		year = time.Now().Year()
	}
	
	// 设置年度筛选条件
	startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.Local)
	
	filter := &model.DiaryFilter{
		StartDate: &startDate,
		EndDate:   &endDate,
	}
	
	diaryEntries, err := s.storage.GetDiaryEntries(filter)
	if err != nil {
		return nil, fmt.Errorf("获取年度日记失败: %w", err)
	}
	
	stats := &model.Statistics{
		TotalDiaryEntries: len(diaryEntries),
		MoodDistribution:  make(map[int]int),
		MonthlyActivity:   make(map[string]int),
	}
	
	// 统计心情分布
	for _, entry := range diaryEntries {
		stats.MoodDistribution[entry.Mood]++
	}
	
	// 统计月度活跃度
	for _, entry := range diaryEntries {
		monthKey := entry.Date.Format("2006-01")
		stats.MonthlyActivity[monthKey]++
	}
	
	return stats, nil
}

// GetMoodTrend 获取心情趋势
func (s *statisticsService) GetMoodTrend(days int) ([]float64, error) {
	if days <= 0 {
		days = 30 // 默认30天
	}
	
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)
	
	filter := &model.DiaryFilter{
		StartDate: &startDate,
		EndDate:   &endDate,
	}
	
	diaryEntries, err := s.storage.GetDiaryEntries(filter)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	// 按日期分组计算平均心情
	dailyMoods := make(map[string][]int)
	for _, entry := range diaryEntries {
		dateKey := entry.Date.Format("2006-01-02")
		dailyMoods[dateKey] = append(dailyMoods[dateKey], entry.Mood)
	}
	
	// 计算每日平均心情
	var trend []float64
	for i := 0; i < days; i++ {
		date := startDate.AddDate(0, 0, i)
		dateKey := date.Format("2006-01-02")
		
		moods := dailyMoods[dateKey]
		if len(moods) == 0 {
			trend = append(trend, 0) // 没有数据的日期
		} else {
			sum := 0
			for _, mood := range moods {
				sum += mood
			}
			average := float64(sum) / float64(len(moods))
			trend = append(trend, average)
		}
	}
	
	return trend, nil
}

// GetTopTags 获取最常用的标签
func (s *statisticsService) GetTopTags(limit int) (map[string]int, error) {
	if limit <= 0 {
		limit = 10
	}
	
	diaryEntries, err := s.storage.GetDiaryEntries(nil)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	tagCount := make(map[string]int)
	for _, entry := range diaryEntries {
		for _, tag := range entry.Tags {
			tagCount[tag]++
		}
	}
	
	// 如果标签数量超过限制，只返回前N个
	if len(tagCount) <= limit {
		return tagCount, nil
	}
	
	// 简单实现：返回所有标签，实际应用中可以排序后取前N个
	return tagCount, nil
}

// GetActivityHeatmap 获取活跃度热力图数据
func (s *statisticsService) GetActivityHeatmap(year int) (map[string]int, error) {
	if year == 0 {
		year = time.Now().Year()
	}
	
	startDate := time.Date(year, 1, 1, 0, 0, 0, 0, time.Local)
	endDate := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.Local)
	
	filter := &model.DiaryFilter{
		StartDate: &startDate,
		EndDate:   &endDate,
	}
	
	diaryEntries, err := s.storage.GetDiaryEntries(filter)
	if err != nil {
		return nil, fmt.Errorf("获取日记列表失败: %w", err)
	}
	
	heatmap := make(map[string]int)
	for _, entry := range diaryEntries {
		dateKey := entry.Date.Format("2006-01-02")
		heatmap[dateKey]++
	}
	
	return heatmap, nil
}
