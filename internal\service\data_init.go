package service

import (
	"time"

	"love-software/internal/model"
)

// InitializeTestData 初始化测试数据
func (sm *ServiceManager) InitializeTestData() error {
	// 检查是否已有数据
	diaries, err := sm.DiaryService.GetEntries(nil)
	if err == nil && len(diaries) > 0 {
		return nil // 已有数据，不需要初始化
	}
	
	// 初始化纪念日数据
	if err := sm.initAnniversaries(); err != nil {
		return err
	}
	
	// 初始化日记数据
	if err := sm.initDiaries(); err != nil {
		return err
	}
	
	// 初始化情话数据
	if err := sm.initQuotes(); err != nil {
		return err
	}
	
	return nil
}

// initAnniversaries 初始化纪念日数据
func (sm *ServiceManager) initAnniversaries() error {
	anniversaries := []*model.Anniversary{
		{
			Name:        "恋爱纪念日",
			Date:        time.Date(2023, 3, 15, 0, 0, 0, 0, time.Local),
			Type:        "恋爱纪念日",
			Description: "我们在一起的第一天",
			IsRecurring: true,
			RemindDays:  7,
		},
		{
			Name:        "第一次约会",
			Date:        time.Date(2023, 2, 20, 0, 0, 0, 0, time.Local),
			Type:        "第一次约会",
			Description: "第一次正式约会，去了电影院",
			IsRecurring: true,
			RemindDays:  3,
		},
		{
			Name:        "生日 (TA)",
			Date:        time.Date(1995, 8, 15, 0, 0, 0, 0, time.Local),
			Type:        "生日",
			Description: "TA的生日",
			IsRecurring: true,
			RemindDays:  7,
		},
		{
			Name:        "第一次牵手",
			Date:        time.Date(2023, 3, 20, 0, 0, 0, 0, time.Local),
			Type:        "里程碑",
			Description: "第一次牵手的美好时刻",
			IsRecurring: true,
			RemindDays:  1,
		},
	}
	
	for _, anniversary := range anniversaries {
		if err := sm.AnniversaryService.CreateAnniversary(anniversary); err != nil {
			return err
		}
	}
	
	return nil
}

// initDiaries 初始化日记数据
func (sm *ServiceManager) initDiaries() error {
	diaries := []*model.DiaryEntry{
		{
			Title:       "第一次约会",
			Content:     "今天是我们第一次正式约会，心情特别紧张又兴奋。我们一起看了电影，还在咖啡厅聊了很久。TA的笑容真的很美，让我的心跳得很快。希望以后还有更多这样美好的时光。",
			Date:        time.Date(2023, 2, 20, 19, 30, 0, 0, time.Local),
			Mood:        5,
			Tags:        []string{"约会", "电影", "咖啡厅"},
			IsImportant: true,
		},
		{
			Title:       "确定关系",
			Content:     "今天我们正式确定了关系！当TA说愿意和我在一起的时候，我觉得整个世界都亮了。这是我人生中最开心的一天，我要好好珍惜这份感情。",
			Date:        time.Date(2023, 3, 15, 20, 0, 0, 0, time.Local),
			Mood:        5,
			Tags:        []string{"表白", "确定关系", "开心"},
			IsImportant: true,
		},
		{
			Title:       "第一次牵手",
			Content:     "今天散步的时候，我们第一次牵手了。TA的手很温暖，让我感到很安心。虽然只是简单的牵手，但对我来说意义重大。",
			Date:        time.Date(2023, 3, 20, 18, 0, 0, 0, time.Local),
			Mood:        4,
			Tags:        []string{"牵手", "散步", "温暖"},
			IsImportant: true,
		},
		{
			Title:       "一起做饭",
			Content:     "今天第一次一起做饭，虽然厨艺都不太好，但过程很有趣。我们一起笑着处理食材，虽然最后做出来的菜味道一般，但心情很好。",
			Date:        time.Date(2023, 4, 2, 12, 0, 0, 0, time.Local),
			Mood:        4,
			Tags:        []string{"做饭", "有趣", "一起"},
			IsImportant: false,
		},
		{
			Title:       "小争吵",
			Content:     "今天因为一些小事情有了争吵，心情有点低落。不过后来我们都冷静下来，好好沟通了。感情中有摩擦是正常的，重要的是如何解决。",
			Date:        time.Date(2023, 4, 10, 21, 0, 0, 0, time.Local),
			Mood:        2,
			Tags:        []string{"争吵", "沟通", "成长"},
			IsImportant: false,
		},
	}
	
	for _, diary := range diaries {
		if err := sm.DiaryService.CreateEntry(diary); err != nil {
			return err
		}
	}
	
	return nil
}

// initQuotes 初始化情话数据
func (sm *ServiceManager) initQuotes() error {
	quotes := []*model.LoveQuote{
		{
			Content:    "遇见你是我最大的幸运，爱上你是我最美的意外。",
			Category:   "表白",
			Source:     "原创",
			IsFavorite: true,
		},
		{
			Content:    "你是我心中最亮的星，照亮了我前进的路。",
			Category:   "浪漫",
			Source:     "原创",
			IsFavorite: true,
		},
		{
			Content:    "愿我们的爱情像美酒一样，越陈越香。",
			Category:   "承诺",
			Source:     "原创",
			IsFavorite: false,
		},
		{
			Content:    "每天醒来第一个想到的人是你，每天睡前最后一个想念的人也是你。",
			Category:   "思念",
			Source:     "原创",
			IsFavorite: true,
		},
		{
			Content:    "谢谢你出现在我的生命里，让我的世界变得如此美好。",
			Category:   "感谢",
			Source:     "原创",
			IsFavorite: false,
		},
		{
			Content:    "和你在一起的每一天都是情人节。",
			Category:   "节日",
			Source:     "原创",
			IsFavorite: true,
		},
		{
			Content:    "你的笑容是我见过最美的风景。",
			Category:   "日常",
			Source:     "原创",
			IsFavorite: false,
		},
		{
			Content:    "我想和你一起慢慢变老，一起看遍世间美景。",
			Category:   "承诺",
			Source:     "原创",
			IsFavorite: true,
		},
	}
	
	for _, quote := range quotes {
		if err := sm.QuoteService.CreateQuote(quote); err != nil {
			return err
		}
	}
	
	return nil
}
