package storage

import (
	"love-software/internal/model"
)

// Storage 存储接口
type Storage interface {
	// 初始化和关闭
	Initialize() error
	Close() error
	
	// 日记相关
	SaveDiaryEntry(entry *model.DiaryEntry) error
	GetDiaryEntry(id string) (*model.DiaryEntry, error)
	GetDiaryEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error)
	UpdateDiaryEntry(entry *model.DiaryEntry) error
	DeleteDiaryEntry(id string) error
	
	// 纪念日相关
	SaveAnniversary(anniversary *model.Anniversary) error
	GetAnniversary(id string) (*model.Anniversary, error)
	GetAnniversaries(filter *model.AnniversaryFilter) ([]*model.Anniversary, error)
	UpdateAnniversary(anniversary *model.Anniversary) error
	DeleteAnniversary(id string) error
	
	// 相册相关
	SavePhotoAlbum(album *model.PhotoAlbum) error
	GetPhotoAlbum(id string) (*model.PhotoAlbum, error)
	GetPhotoAlbums() ([]*model.PhotoAlbum, error)
	UpdatePhotoAlbum(album *model.PhotoAlbum) error
	DeletePhotoAlbum(id string) error
	
	// 照片相关
	SavePhoto(photo *model.Photo) error
	GetPhoto(id string) (*model.Photo, error)
	GetPhotos(filter *model.PhotoFilter) ([]*model.Photo, error)
	UpdatePhoto(photo *model.Photo) error
	DeletePhoto(id string) error
	
	// 情话相关
	SaveLoveQuote(quote *model.LoveQuote) error
	GetLoveQuote(id string) (*model.LoveQuote, error)
	GetLoveQuotes(filter *model.QuoteFilter) ([]*model.LoveQuote, error)
	UpdateLoveQuote(quote *model.LoveQuote) error
	DeleteLoveQuote(id string) error
	
	// 配置相关
	SaveConfig(config *model.UserConfig) error
	GetConfig() (*model.UserConfig, error)
	
	// 通知相关
	SaveNotification(notification *model.Notification) error
	GetNotifications(unreadOnly bool) ([]*model.Notification, error)
	UpdateNotification(notification *model.Notification) error
	DeleteNotification(id string) error
	
	// 备份相关
	CreateBackup() (*model.BackupInfo, error)
	RestoreBackup(backupID string) error
	GetBackups() ([]*model.BackupInfo, error)
	DeleteBackup(backupID string) error
}
