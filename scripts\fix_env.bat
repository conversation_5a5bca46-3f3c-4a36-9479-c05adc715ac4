@echo off
echo 💕 爱情个人软件 - 环境修复脚本
echo ================================

echo 🔧 正在检查和修复开发环境...
echo.

:: 检查Go环境
echo [1/5] 检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Go未安装或未在PATH中
    echo 💡 请从以下地址下载安装Go:
    echo    https://golang.org/dl/
    pause
    exit /b 1
) else (
    echo ✅ Go环境正常
    go version
)

echo.

:: 检查和设置CGO
echo [2/5] 检查CGO设置...
for /f "tokens=*" %%i in ('go env CGO_ENABLED') do set CGO_STATUS=%%i
if "%CGO_STATUS%"=="0" (
    echo 🔧 启用CGO...
    go env -w CGO_ENABLED=1
    echo ✅ CGO已启用
) else (
    echo ✅ CGO已启用
)

echo.

:: 检查GCC编译器
echo [3/5] 检查GCC编译器...
gcc --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ GCC编译器未找到
    echo.
    echo 💡 请选择以下解决方案之一:
    echo.
    echo 方案1: 安装TDM-GCC (推荐)
    echo   1. 访问: https://jmeubank.github.io/tdm-gcc/
    echo   2. 下载并安装最新版本
    echo   3. 重启命令行窗口
    echo.
    echo 方案2: 安装MSYS2
    echo   1. 访问: https://www.msys2.org/
    echo   2. 安装MSYS2
    echo   3. 运行: pacman -S mingw-w64-x86_64-gcc
    echo   4. 添加 C:\msys64\mingw64\bin 到PATH
    echo.
    echo 方案3: 使用Visual Studio Build Tools
    echo   1. 安装Visual Studio Build Tools
    echo   2. 选择C++ build tools组件
    echo.
    echo 📖 详细说明请查看: docs\环境配置指南.md
    echo.
    pause
    exit /b 1
) else (
    echo ✅ GCC编译器正常
    gcc --version | findstr "gcc"
)

echo.

:: 检查网络和代理设置
echo [4/5] 检查Go模块代理...
for /f "tokens=*" %%i in ('go env GOPROXY') do set GOPROXY_STATUS=%%i
if "%GOPROXY_STATUS%"=="direct" (
    echo 🔧 设置Go代理以加速下载...
    go env -w GOPROXY=https://goproxy.cn,direct
    echo ✅ Go代理已设置
) else (
    echo ✅ Go代理设置正常: %GOPROXY_STATUS%
)

echo.

:: 测试编译
echo [5/5] 测试编译环境...
echo 🧪 创建测试程序...

:: 创建临时测试文件
echo package main > test_cgo.go
echo import "C" >> test_cgo.go
echo func main() {} >> test_cgo.go

echo 🔨 测试CGO编译...
go build -o test_cgo.exe test_cgo.go >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ CGO编译测试失败
    echo 💡 可能的问题:
    echo    1. GCC编译器未正确安装
    echo    2. PATH环境变量未包含编译器路径
    echo    3. 需要重启命令行窗口
    del test_cgo.go >nul 2>&1
    pause
    exit /b 1
) else (
    echo ✅ CGO编译测试通过
)

:: 清理测试文件
del test_cgo.go >nul 2>&1
del test_cgo.exe >nul 2>&1

echo.
echo 🎉 环境检查完成！所有组件正常工作
echo.

:: 询问是否立即运行程序
echo 🚀 是否立即运行爱情个人软件? (Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    echo.
    echo 🎯 启动程序...
    go run cmd/main.go
) else (
    echo.
    echo 💡 环境已准备就绪，您可以运行以下命令启动程序:
    echo    go run cmd/main.go
    echo    或者运行: scripts\run.bat
)

echo.
pause
