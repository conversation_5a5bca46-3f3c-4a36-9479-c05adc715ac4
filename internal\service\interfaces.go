package service

import (
	"love-software/internal/model"
)

// DiaryService 日记服务接口
type DiaryService interface {
	// CreateEntry 创建日记条目
	CreateEntry(entry *model.DiaryEntry) error
	
	// GetEntry 获取单个日记条目
	GetEntry(id string) (*model.DiaryEntry, error)
	
	// GetEntries 获取日记条目列表
	GetEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error)
	
	// UpdateEntry 更新日记条目
	UpdateEntry(entry *model.DiaryEntry) error
	
	// DeleteEntry 删除日记条目
	DeleteEntry(id string) error
	
	// SearchEntries 搜索日记条目
	SearchEntries(keyword string) ([]*model.DiaryEntry, error)
}

// AnniversaryService 纪念日服务接口
type AnniversaryService interface {
	// CreateAnniversary 创建纪念日
	CreateAnniversary(anniversary *model.Anniversary) error
	
	// GetAnniversary 获取单个纪念日
	GetAnniversary(id string) (*model.Anniversary, error)
	
	// GetAnniversaries 获取纪念日列表
	GetAnniversaries(filter *model.AnniversaryFilter) ([]*model.Anniversary, error)
	
	// UpdateAnniversary 更新纪念日
	UpdateAnniversary(anniversary *model.Anniversary) error
	
	// DeleteAnniversary 删除纪念日
	DeleteAnniversary(id string) error
	
	// GetUpcomingAnniversaries 获取即将到来的纪念日
	GetUpcomingAnniversaries(days int) ([]*model.Anniversary, error)
}

// PhotoService 照片服务接口
type PhotoService interface {
	// CreateAlbum 创建相册
	CreateAlbum(album *model.PhotoAlbum) error
	
	// GetAlbum 获取相册
	GetAlbum(id string) (*model.PhotoAlbum, error)
	
	// GetAlbums 获取相册列表
	GetAlbums() ([]*model.PhotoAlbum, error)
	
	// UpdateAlbum 更新相册
	UpdateAlbum(album *model.PhotoAlbum) error
	
	// DeleteAlbum 删除相册
	DeleteAlbum(id string) error
	
	// AddPhoto 添加照片到相册
	AddPhoto(albumID string, photo *model.Photo) error
	
	// GetPhoto 获取照片
	GetPhoto(id string) (*model.Photo, error)
	
	// GetPhotos 获取照片列表
	GetPhotos(filter *model.PhotoFilter) ([]*model.Photo, error)
	
	// UpdatePhoto 更新照片信息
	UpdatePhoto(photo *model.Photo) error
	
	// DeletePhoto 删除照片
	DeletePhoto(id string) error
}

// QuoteService 情话服务接口
type QuoteService interface {
	// CreateQuote 创建情话
	CreateQuote(quote *model.LoveQuote) error
	
	// GetQuote 获取单个情话
	GetQuote(id string) (*model.LoveQuote, error)
	
	// GetQuotes 获取情话列表
	GetQuotes(filter *model.QuoteFilter) ([]*model.LoveQuote, error)
	
	// UpdateQuote 更新情话
	UpdateQuote(quote *model.LoveQuote) error
	
	// DeleteQuote 删除情话
	DeleteQuote(id string) error
	
	// GetRandomQuote 获取随机情话
	GetRandomQuote() (*model.LoveQuote, error)
	
	// GetFavoriteQuotes 获取收藏的情话
	GetFavoriteQuotes() ([]*model.LoveQuote, error)
}

// ConfigService 配置服务接口
type ConfigService interface {
	// GetConfig 获取配置
	GetConfig() (*model.UserConfig, error)
	
	// UpdateConfig 更新配置
	UpdateConfig(config *model.UserConfig) error
	
	// ResetConfig 重置配置
	ResetConfig() error
}

// StatisticsService 统计服务接口
type StatisticsService interface {
	// GetStatistics 获取统计信息
	GetStatistics() (*model.Statistics, error)
	
	// GetMoodDistribution 获取心情分布
	GetMoodDistribution() (map[int]int, error)
	
	// GetMonthlyActivity 获取月度活跃度
	GetMonthlyActivity() (map[string]int, error)
	
	// GetLoveDays 获取恋爱天数
	GetLoveDays() (int, error)
}

// NotificationService 通知服务接口
type NotificationService interface {
	// CreateNotification 创建通知
	CreateNotification(notification *model.Notification) error
	
	// GetNotifications 获取通知列表
	GetNotifications(unreadOnly bool) ([]*model.Notification, error)
	
	// MarkAsRead 标记为已读
	MarkAsRead(id string) error
	
	// DeleteNotification 删除通知
	DeleteNotification(id string) error
	
	// CheckAnniversaryReminders 检查纪念日提醒
	CheckAnniversaryReminders() error
}

// BackupService 备份服务接口
type BackupService interface {
	// CreateBackup 创建备份
	CreateBackup() (*model.BackupInfo, error)
	
	// RestoreBackup 恢复备份
	RestoreBackup(backupID string) error
	
	// GetBackups 获取备份列表
	GetBackups() ([]*model.BackupInfo, error)
	
	// DeleteBackup 删除备份
	DeleteBackup(backupID string) error
	
	// AutoBackup 自动备份
	AutoBackup() error
}
