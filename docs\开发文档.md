# 💕 爱情个人软件 - 开发文档

## 📋 目录
1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [UI设计规范](#ui设计规范)
4. [功能模块详解](#功能模块详解)
5. [数据存储方案](#数据存储方案)
6. [开发环境配置](#开发环境配置)
7. [编码规范](#编码规范)

## 🎯 项目概述

### 项目目标
开发一款温馨浪漫的桌面端爱情管理软件，帮助用户记录恋爱生活中的点点滴滴，增进情侣间的感情交流。

### 目标用户
- 热恋中的情侣
- 新婚夫妇
- 希望记录美好时光的恋人

### 核心价值
- 私密性：所有数据本地存储，保护隐私
- 便捷性：简洁直观的操作界面
- 完整性：全方位记录恋爱生活
- 美观性：温馨浪漫的视觉设计

## 🏗 技术架构

### 整体架构
```
┌─────────────────┐
│   用户界面层     │  Fyne GUI Framework
├─────────────────┤
│   业务逻辑层     │  Service Layer
├─────────────────┤
│   数据访问层     │  Storage Layer
├─────────────────┤
│   数据存储层     │  Local Files / Redis
└─────────────────┘
```

### 技术选型理由

#### Fyne GUI框架
- **优势**：Go原生、跨平台、现代化UI
- **适用性**：适合桌面应用开发
- **性能**：轻量级，启动快速

#### 数据存储双方案
- **本地存储**：隐私保护，离线可用
- **Redis存储**：高性能，支持复杂查询

### 依赖包管理
```go
// 主要依赖
fyne.io/fyne/v2                 // GUI框架
github.com/go-redis/redis/v8    // Redis客户端
github.com/mattn/go-sqlite3     // SQLite数据库
github.com/google/uuid          // UUID生成
```

## 🎨 UI设计规范

### 设计理念
- **温馨浪漫**：粉色系主题，圆角设计
- **简洁明了**：减少复杂操作，突出核心功能
- **情感化**：使用爱心、花朵等元素装饰

### 色彩方案
```
主色调：#FF69B4 (热粉色)
辅助色：#FFB6C1 (浅粉色)
背景色：#FFF0F5 (薰衣草红)
文字色：#2F2F2F (深灰色)
强调色：#DC143C (深红色)
```

### 字体规范
- **标题字体**：18-24px，加粗
- **正文字体**：14-16px，常规
- **小字体**：12px，用于提示信息

### 布局原则
- **网格系统**：12列网格布局
- **间距统一**：8px基础间距单位
- **响应式**：适配不同屏幕尺寸

## 🔧 功能模块详解

### 1. 恋爱日记模块
```go
type DiaryEntry struct {
    ID          string    `json:"id"`
    Date        time.Time `json:"date"`
    Title       string    `json:"title"`
    Content     string    `json:"content"`
    Mood        int       `json:"mood"`        // 1-5心情评分
    Images      []string  `json:"images"`      // 图片路径列表
    Tags        []string  `json:"tags"`        // 标签
    IsImportant bool      `json:"is_important"` // 重要标记
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

**功能特性**：
- 富文本编辑器
- 图片拖拽上传
- 心情评分系统
- 标签分类管理
- 搜索和筛选

### 2. 纪念日管理模块
```go
type Anniversary struct {
    ID          string    `json:"id"`
    Name        string    `json:"name"`
    Date        time.Time `json:"date"`
    Type        string    `json:"type"`        // 恋爱、结婚、生日等
    Description string    `json:"description"`
    IsRecurring bool      `json:"is_recurring"` // 是否每年重复
    RemindDays  int       `json:"remind_days"`  // 提前提醒天数
    CreatedAt   time.Time `json:"created_at"`
}
```

**功能特性**：
- 倒计时显示
- 提醒通知
- 历史回顾
- 自定义类型

### 3. 照片收藏模块
```go
type PhotoAlbum struct {
    ID          string    `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    CoverImage  string    `json:"cover_image"`
    Photos      []Photo   `json:"photos"`
    CreatedAt   time.Time `json:"created_at"`
}

type Photo struct {
    ID          string    `json:"id"`
    FilePath    string    `json:"file_path"`
    Caption     string    `json:"caption"`
    TakenAt     time.Time `json:"taken_at"`
    Tags        []string  `json:"tags"`
    Location    string    `json:"location"`
}
```

**功能特性**：
- 相册管理
- 照片标签
- 幻灯片播放
- 批量操作

### 4. 情话收藏模块
```go
type LoveQuote struct {
    ID        string    `json:"id"`
    Content   string    `json:"content"`
    Category  string    `json:"category"`  // 表白、日常、节日等
    Source    string    `json:"source"`    // 来源
    IsFavorite bool     `json:"is_favorite"`
    CreatedAt time.Time `json:"created_at"`
}
```

**功能特性**：
- 分类管理
- 随机显示
- 收藏功能
- 导出分享

## 💾 数据存储方案

### 方案一：本地文件存储
```
data/
├── config.json          # 应用配置
├── diary/              # 日记数据
│   ├── 2024-01.json
│   └── 2024-02.json
├── anniversaries.json   # 纪念日数据
├── albums/             # 相册数据
│   ├── album1.json
│   └── photos/         # 照片文件
├── quotes.json         # 情话数据
└── backup/             # 备份目录
```

**优势**：
- 完全离线
- 数据私密
- 易于备份

### 方案二：Redis存储
```
Keys设计：
user:{user_id}:diary:{date}     # 日记条目
user:{user_id}:anniversaries    # 纪念日列表
user:{user_id}:albums          # 相册列表
user:{user_id}:quotes          # 情话收藏
user:{user_id}:config          # 用户配置
```

**优势**：
- 高性能查询
- 支持复杂操作
- 数据持久化

## 🛠 开发环境配置

### 必需软件
1. **Go 1.21+**
   ```bash
   # 验证安装
   go version
   ```

2. **Git**
   ```bash
   # 验证安装
   git --version
   ```

3. **IDE推荐**
   - VS Code + Go扩展
   - GoLand
   - Vim/Neovim + vim-go

### 项目初始化
```bash
# 创建项目目录
mkdir love-software
cd love-software

# 初始化Go模块
go mod init love-software

# 安装Fyne工具
go install fyne.io/fyne/v2/cmd/fyne@latest
```

### 开发工具配置
```bash
# 安装开发工具
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/air-verse/air@latest  # 热重载
```

## 📝 编码规范

### Go代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 变量命名使用驼峰命名法
- 包名使用小写字母

### 项目结构规范
- internal/：内部包，不对外暴露
- cmd/：应用程序入口
- pkg/：可复用的库代码
- assets/：静态资源文件

### 注释规范
```go
// Package service 提供业务逻辑服务
package service

// DiaryService 日记服务接口
type DiaryService interface {
    // CreateEntry 创建新的日记条目
    CreateEntry(entry *model.DiaryEntry) error
    
    // GetEntries 获取日记条目列表
    GetEntries(filter *DiaryFilter) ([]*model.DiaryEntry, error)
}
```

### 错误处理
```go
// 统一错误处理
func (s *diaryService) CreateEntry(entry *model.DiaryEntry) error {
    if err := s.validate(entry); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }
    
    if err := s.storage.Save(entry); err != nil {
        return fmt.Errorf("save entry failed: %w", err)
    }
    
    return nil
}
```

---

📝 **文档版本**: v1.0  
📅 **更新时间**: 2024年  
👨‍💻 **维护者**: 开发团队
