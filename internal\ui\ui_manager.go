package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"love-software/internal/service"
)

// PhotoUI 照片界面占位符
type PhotoUI struct {
	service service.PhotoService
}

func NewPhotoUI(service service.PhotoService) *PhotoUI {
	return &PhotoUI{service: service}
}

func (ui *PhotoUI) CreateView() *container.VBox {
	title := widget.NewLabelWithStyle("📸 照片收藏", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	content := widget.NewLabel("照片收藏功能开发中...")
	return container.NewVBox(title, widget.NewSeparator(), content)
}

// QuoteUI 情话界面占位符
type QuoteUI struct {
	service service.QuoteService
}

func NewQuoteUI(service service.QuoteService) *QuoteUI {
	return &QuoteUI{service: service}
}

func (ui *QuoteUI) CreateView() *container.VBox {
	title := widget.NewLabelWithStyle("💌 情话收藏", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	content := widget.NewLabel("情话收藏功能开发中...")
	return container.NewVBox(title, widget.NewSeparator(), content)
}

// SettingsUI 设置界面占位符
type SettingsUI struct {
	service service.ConfigService
}

func NewSettingsUI(service service.ConfigService) *SettingsUI {
	return &SettingsUI{service: service}
}

func (ui *SettingsUI) CreateView() *container.VBox {
	title := widget.NewLabelWithStyle("⚙️ 设置", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	content := widget.NewLabel("设置功能开发中...")
	return container.NewVBox(title, widget.NewSeparator(), content)
}

// UIManager UI管理器
type UIManager struct {
	window   fyne.Window
	services *service.ServiceManager
	
	// 主要界面组件
	navigationMenu *container.VBox
	mainContent    *container.Stack
	currentView    string
	
	// 各模块UI
	diaryUI       *DiaryUI
	anniversaryUI *AnniversaryUI
	photoUI       *PhotoUI
	quoteUI       *QuoteUI
	settingsUI    *SettingsUI
}

// NewUIManager 创建UI管理器
func NewUIManager(window fyne.Window, services *service.ServiceManager) *UIManager {
	ui := &UIManager{
		window:   window,
		services: services,
	}
	
	// 初始化各模块UI
	ui.diaryUI = NewDiaryUI(services.DiaryService)
	ui.anniversaryUI = NewAnniversaryUI(services.AnniversaryService)
	ui.photoUI = NewPhotoUI(services.PhotoService)
	ui.quoteUI = NewQuoteUI(services.QuoteService)
	ui.settingsUI = NewSettingsUI(services.ConfigService)
	
	return ui
}

// CreateNavigationMenu 创建导航菜单
func (ui *UIManager) CreateNavigationMenu() *container.VBox {
	// 创建导航按钮
	homeBtn := widget.NewButtonWithIcon("首页", theme.HomeIcon(), func() {
		ui.showView("home")
	})
	
	diaryBtn := widget.NewButtonWithIcon("恋爱日记", theme.DocumentIcon(), func() {
		ui.showView("diary")
	})
	
	anniversaryBtn := widget.NewButtonWithIcon("纪念日", theme.HistoryIcon(), func() {
		ui.showView("anniversary")
	})
	
	photoBtn := widget.NewButtonWithIcon("照片收藏", theme.ViewFullScreenIcon(), func() {
		ui.showView("photo")
	})
	
	quoteBtn := widget.NewButtonWithIcon("情话收藏", theme.MailComposeIcon(), func() {
		ui.showView("quote")
	})
	
	statsBtn := widget.NewButtonWithIcon("统计分析", theme.InfoIcon(), func() {
		ui.showView("statistics")
	})
	
	settingsBtn := widget.NewButtonWithIcon("设置", theme.SettingsIcon(), func() {
		ui.showView("settings")
	})
	
	// 设置按钮样式
	buttons := []*widget.Button{homeBtn, diaryBtn, anniversaryBtn, photoBtn, quoteBtn, statsBtn, settingsBtn}
	for _, btn := range buttons {
		btn.Alignment = widget.ButtonAlignLeading
		btn.Resize(fyne.NewSize(200, 40))
	}
	
	// 创建导航容器
	ui.navigationMenu = container.NewVBox(
		widget.NewCard("", "", container.NewVBox(
			widget.NewLabelWithStyle("💕 爱情软件", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
			widget.NewSeparator(),
		)),
		homeBtn,
		diaryBtn,
		anniversaryBtn,
		photoBtn,
		quoteBtn,
		widget.NewSeparator(),
		statsBtn,
		settingsBtn,
	)
	
	return ui.navigationMenu
}

// CreateMainContent 创建主内容区域
func (ui *UIManager) CreateMainContent() *container.Stack {
	// 创建各个视图
	homeView := ui.createHomeView()
	diaryView := ui.diaryUI.CreateView()
	anniversaryView := ui.anniversaryUI.CreateView()
	photoView := ui.photoUI.CreateView()
	quoteView := ui.quoteUI.CreateView()
	statisticsView := ui.createStatisticsView()
	settingsView := ui.settingsUI.CreateView()
	
	// 创建堆栈容器
	ui.mainContent = container.NewStack(
		homeView,
		diaryView,
		anniversaryView,
		photoView,
		quoteView,
		statisticsView,
		settingsView,
	)
	
	// 默认显示首页
	ui.showView("home")
	
	return ui.mainContent
}

// showView 显示指定视图
func (ui *UIManager) showView(viewName string) {
	ui.currentView = viewName
	
	// 隐藏所有视图
	for _, obj := range ui.mainContent.Objects {
		obj.Hide()
	}
	
	// 显示指定视图
	switch viewName {
	case "home":
		ui.mainContent.Objects[0].Show()
	case "diary":
		ui.mainContent.Objects[1].Show()
	case "anniversary":
		ui.mainContent.Objects[2].Show()
	case "photo":
		ui.mainContent.Objects[3].Show()
	case "quote":
		ui.mainContent.Objects[4].Show()
	case "statistics":
		ui.mainContent.Objects[5].Show()
	case "settings":
		ui.mainContent.Objects[6].Show()
	}
}

// createHomeView 创建首页视图
func (ui *UIManager) createHomeView() *container.VBox {
	// 欢迎标题
	title := widget.NewLabelWithStyle("💕 欢迎使用爱情个人软件", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	
	// 快速统计卡片
	statsCard := widget.NewCard("📊 快速统计", "", container.NewGridWithColumns(2,
		widget.NewLabel("恋爱天数: 365天"),
		widget.NewLabel("日记条目: 128篇"),
		widget.NewLabel("珍贵照片: 256张"),
		widget.NewLabel("甜蜜情话: 64条"),
	))
	
	// 最近活动
	recentCard := widget.NewCard("📝 最近活动", "", container.NewVBox(
		widget.NewLabel("• 今天添加了一篇日记"),
		widget.NewLabel("• 昨天上传了3张照片"),
		widget.NewLabel("• 3天前收藏了一句情话"),
	))
	
	// 即将到来的纪念日
	upcomingCard := widget.NewCard("🎉 即将到来", "", container.NewVBox(
		widget.NewLabel("• 恋爱一周年 (还有30天)"),
		widget.NewLabel("• 生日 (还有15天)"),
		widget.NewLabel("• 第一次约会纪念日 (还有7天)"),
	))
	
	// 快速操作按钮
	quickActions := container.NewGridWithColumns(2,
		widget.NewButton("📝 写日记", func() {
			ui.showView("diary")
		}),
		widget.NewButton("📸 添加照片", func() {
			ui.showView("photo")
		}),
		widget.NewButton("💌 收藏情话", func() {
			ui.showView("quote")
		}),
		widget.NewButton("🎉 添加纪念日", func() {
			ui.showView("anniversary")
		}),
	)
	
	return container.NewVBox(
		title,
		widget.NewSeparator(),
		container.NewGridWithColumns(2, statsCard, recentCard),
		upcomingCard,
		widget.NewCard("🚀 快速操作", "", quickActions),
	)
}

// createStatisticsView 创建统计视图
func (ui *UIManager) createStatisticsView() *container.VBox {
	title := widget.NewLabelWithStyle("📊 统计分析", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	
	// 基础统计
	basicStats := widget.NewCard("基础统计", "", container.NewGridWithColumns(2,
		widget.NewLabel("总日记数: 128"),
		widget.NewLabel("总照片数: 256"),
		widget.NewLabel("总情话数: 64"),
		widget.NewLabel("纪念日数: 12"),
	))
	
	// 心情分布
	moodStats := widget.NewCard("心情分布", "", container.NewVBox(
		widget.NewProgressBar(), // TODO: 实现真实的心情分布图表
		widget.NewLabel("😊 开心: 45%"),
		widget.NewLabel("😍 甜蜜: 30%"),
		widget.NewLabel("😌 平静: 20%"),
		widget.NewLabel("😢 难过: 5%"),
	))
	
	// 月度活跃度
	activityStats := widget.NewCard("月度活跃度", "", container.NewVBox(
		widget.NewLabel("本月新增日记: 12篇"),
		widget.NewLabel("本月上传照片: 24张"),
		widget.NewLabel("本月收藏情话: 6条"),
	))
	
	return container.NewVBox(
		title,
		widget.NewSeparator(),
		basicStats,
		container.NewGridWithColumns(2, moodStats, activityStats),
	)
}
