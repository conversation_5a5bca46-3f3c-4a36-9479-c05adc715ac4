package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/theme"
	"fyne.io/fyne/v2/widget"

	"love-software/internal/service"
)

// AnniversaryUI 纪念日界面
type AnniversaryUI struct {
	service service.AnniversaryService
}

// NewAnniversaryUI 创建纪念日界面
func NewAnniversaryUI(service service.AnniversaryService) *AnniversaryUI {
	return &AnniversaryUI{
		service: service,
	}
}

// CreateView 创建纪念日视图
func (ui *AnniversaryUI) CreateView() *container.VBox {
	// 标题栏
	title := widget.NewLabelWithStyle("🎉 纪念日管理", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})
	
	// 工具栏
	toolbar := container.NewHBox(
		widget.NewButtonWithIcon("添加纪念日", theme.ContentAddIcon(), func() {
			ui.showAddDialog()
		}),
		widget.NewButtonWithIcon("即将到来", theme.HistoryIcon(), func() {
			ui.showUpcoming()
		}),
	)
	
	// 即将到来的纪念日
	upcomingCard := widget.NewCard("🔔 即将到来", "", container.NewVBox(
		ui.createCountdownItem("恋爱一周年", "还有 30 天", "2024-03-15"),
		ui.createCountdownItem("生日", "还有 15 天", "2024-02-28"),
		ui.createCountdownItem("第一次约会", "还有 7 天", "2024-02-20"),
	))
	
	// 纪念日列表
	anniversaryList := ui.createAnniversaryList()
	
	return container.NewVBox(
		title,
		widget.NewSeparator(),
		toolbar,
		upcomingCard,
		widget.NewCard("📅 所有纪念日", "", anniversaryList),
	)
}

// createCountdownItem 创建倒计时项目
func (ui *AnniversaryUI) createCountdownItem(name, countdown, date string) *container.HBox {
	return container.NewHBox(
		widget.NewIcon(theme.HistoryIcon()),
		widget.NewLabel(name),
		widget.NewSeparator(),
		widget.NewLabelWithStyle(countdown, fyne.TextAlignTrailing, fyne.TextStyle{Bold: true}),
		widget.NewLabel(date),
	)
}

// createAnniversaryList 创建纪念日列表
func (ui *AnniversaryUI) createAnniversaryList() *widget.List {
	// 模拟数据
	anniversaries := []map[string]string{
		{"name": "恋爱纪念日", "date": "2023-03-15", "type": "恋爱"},
		{"name": "第一次约会", "date": "2023-02-20", "type": "约会"},
		{"name": "生日 (TA)", "date": "1995-02-28", "type": "生日"},
		{"name": "生日 (我)", "date": "1994-08-10", "type": "生日"},
		{"name": "第一次牵手", "date": "2023-03-20", "type": "里程碑"},
	}
	
	list := widget.NewList(
		func() int {
			return len(anniversaries)
		},
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewIcon(theme.HistoryIcon()),
				container.NewVBox(
					widget.NewLabel("纪念日名称"),
					widget.NewLabel("日期 | 类型"),
				),
				widget.NewSeparator(),
				container.NewVBox(
					widget.NewButtonWithIcon("", theme.DocumentCreateIcon(), nil),
					widget.NewButtonWithIcon("", theme.DeleteIcon(), nil),
				),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			container := obj.(*container.HBox)
			infoContainer := container.Objects[1].(*container.VBox)
			buttonContainer := container.Objects[3].(*container.VBox)
			
			nameLabel := infoContainer.Objects[0].(*widget.Label)
			dateLabel := infoContainer.Objects[1].(*widget.Label)
			editBtn := buttonContainer.Objects[0].(*widget.Button)
			deleteBtn := buttonContainer.Objects[1].(*widget.Button)
			
			anniversary := anniversaries[id]
			nameLabel.SetText(anniversary["name"])
			dateLabel.SetText(anniversary["date"] + " | " + anniversary["type"])
			
			editBtn.OnTapped = func() {
				ui.showEditDialog(id)
			}
			deleteBtn.OnTapped = func() {
				ui.showDeleteConfirm(id)
			}
		},
	)
	
	return list
}

// showAddDialog 显示添加纪念日对话框
func (ui *AnniversaryUI) showAddDialog() {
	// TODO: 实现添加纪念日对话框
}

// showEditDialog 显示编辑纪念日对话框
func (ui *AnniversaryUI) showEditDialog(id int) {
	// TODO: 实现编辑纪念日对话框
}

// showDeleteConfirm 显示删除确认对话框
func (ui *AnniversaryUI) showDeleteConfirm(id int) {
	// TODO: 实现删除确认对话框
}

// showUpcoming 显示即将到来的纪念日
func (ui *AnniversaryUI) showUpcoming() {
	// TODO: 实现即将到来的纪念日详细视图
}
