# 💕 爱情个人软件 - 环境配置指南

## 🚨 常见问题解决

### 问题：CGO编译错误
```
build constraints exclude all Go files in github.com/go-gl/gl
```

这是因为Fyne GUI框架需要CGO支持，但系统缺少C编译器。

## 🛠 解决方案

### 方案一：安装TDM-GCC (推荐)

1. **下载TDM-GCC**
   - 访问：https://jmeubank.github.io/tdm-gcc/
   - 下载最新版本的TDM-GCC安装包

2. **安装TDM-GCC**
   - 运行安装程序
   - 选择"Create"创建新安装
   - 选择"MinGW-w64"版本
   - 安装到默认路径（通常是 C:\TDM-GCC-64）

3. **验证安装**
   ```bash
   gcc --version
   ```

### 方案二：使用MSYS2

1. **下载MSYS2**
   - 访问：https://www.msys2.org/
   - 下载并安装MSYS2

2. **安装编译工具**
   ```bash
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-pkg-config
   ```

3. **添加到PATH**
   - 将 `C:\msys64\mingw64\bin` 添加到系统PATH

### 方案三：使用Visual Studio Build Tools

1. **下载Visual Studio Build Tools**
   - 访问：https://visualstudio.microsoft.com/downloads/
   - 下载"Build Tools for Visual Studio"

2. **安装C++工具**
   - 选择"C++ build tools"
   - 安装MSVC编译器和Windows SDK

## 🔧 环境变量配置

### 设置CGO环境变量
```bash
# 启用CGO
set CGO_ENABLED=1

# 设置编译器（如果使用TDM-GCC）
set CC=gcc
set CXX=g++
```

### 验证环境
```bash
# 检查Go环境
go env CGO_ENABLED

# 检查编译器
gcc --version
```

## 🚀 运行程序

### 方法一：直接运行
```bash
cd D:\桌面\爱情电脑软件
go run cmd/main.go
```

### 方法二：使用脚本
```bash
# 运行脚本会自动设置环境变量
scripts\run.bat
```

### 方法三：先构建再运行
```bash
# 构建
go build -o love-software.exe cmd/main.go

# 运行
love-software.exe
```

## 🐛 故障排除

### 如果仍然出现CGO错误

1. **重启命令行**
   - 安装编译器后需要重启命令行窗口

2. **检查PATH**
   ```bash
   echo %PATH%
   ```
   确保编译器路径在PATH中

3. **手动设置编译器**
   ```bash
   set CC=C:\TDM-GCC-64\bin\gcc.exe
   set CXX=C:\TDM-GCC-64\bin\g++.exe
   ```

4. **清理模块缓存**
   ```bash
   go clean -modcache
   go mod download
   ```

### 如果出现其他依赖问题

1. **更新Go模块**
   ```bash
   go mod tidy
   go mod download
   ```

2. **设置Go代理**
   ```bash
   go env -w GOPROXY=https://goproxy.cn,direct
   ```

## 📋 完整的环境检查清单

- [ ] Go 1.21+ 已安装
- [ ] CGO_ENABLED=1
- [ ] GCC编译器已安装并在PATH中
- [ ] 可以运行 `gcc --version`
- [ ] 可以运行 `go env CGO_ENABLED` 返回 "1"
- [ ] 网络连接正常（用于下载依赖）

## 🎯 快速解决脚本

创建一个批处理文件 `fix_env.bat`：

```batch
@echo off
echo 🔧 检查和修复环境...

echo 检查Go环境...
go version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Go未安装或未在PATH中
    pause
    exit /b 1
)

echo 检查CGO...
go env CGO_ENABLED
if "%CGO_ENABLED%"=="0" (
    echo 🔧 启用CGO...
    go env -w CGO_ENABLED=1
)

echo 检查GCC...
gcc --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ GCC未安装，请安装TDM-GCC
    echo 下载地址: https://jmeubank.github.io/tdm-gcc/
    pause
    exit /b 1
)

echo ✅ 环境检查完成！
echo 🚀 尝试运行程序...
go run cmd/main.go
```

## 💡 建议

1. **推荐使用TDM-GCC**：最简单的解决方案
2. **重启命令行**：安装编译器后务必重启
3. **使用构建脚本**：`scripts\build.bat` 已配置好环境
4. **检查防火墙**：确保Go可以下载依赖包

按照以上步骤操作后，程序应该可以正常运行！
