# 💕 爱情个人软件 - 快速启动指南

## 🎯 项目概述

这是一个基于Go语言和Fyne GUI框架开发的桌面端爱情管理软件，帮助情侣记录恋爱生活中的美好时光。

## 🛠 环境要求

### 必需软件
- **Go 1.21+** - [下载地址](https://golang.org/dl/)
- **Git** - [下载地址](https://git-scm.com/downloads)
- **GCC编译器** (用于CGO) - **重要！Fyne GUI需要**
  - 推荐：[TDM-GCC](https://jmeubank.github.io/tdm-gcc/)
  - 备选：[MSYS2](https://www.msys2.org/)
  - 备选：Visual Studio Build Tools

### 可选软件
- **Redis** (如果选择Redis存储) - [下载地址](https://redis.io/download)
- **VS Code** + Go扩展 (推荐IDE)

## 🚨 遇到CGO错误？快速解决！

如果您遇到类似以下错误：
```
build constraints exclude all Go files in github.com/go-gl/gl
```

**立即解决方案**：
1. 运行环境修复脚本：`scripts\fix_env.bat`
2. 或按照 `docs\环境配置指南.md` 安装TDM-GCC
3. 重启命令行窗口后重试

## 🚀 快速开始

### 1. 环境验证
```bash
# 检查Go版本
go version

# 检查Git版本
git --version

# 检查GCC版本 (用于CGO)
gcc --version
```

### 2. 项目初始化
```bash
# 进入项目目录
cd love-software

# 安装依赖
go mod tidy

# 验证依赖
go mod verify
```

### 3. 运行程序

#### 方式一：使用脚本 (推荐)
```bash
# Windows
scripts\run.bat

# 或者直接双击 scripts/run.bat 文件
```

#### 方式二：命令行运行
```bash
# 直接运行
go run cmd/main.go

# 或者先构建再运行
go build -o love-software.exe cmd/main.go
./love-software.exe
```

### 4. 构建可执行文件

#### 使用构建脚本 (推荐)
```bash
# Windows
scripts\build.bat

# 构建完成后，可执行文件位于 dist/love-software.exe
```

#### 手动构建
```bash
# 基础构建
go build -o love-software.exe cmd/main.go

# 优化构建 (减小文件大小)
go build -ldflags "-s -w -H windowsgui" -o love-software.exe cmd/main.go
```

## 📁 项目结构说明

```
love-software/
├── cmd/                    # 应用程序入口
│   └── main.go            # 主程序文件
├── internal/              # 内部包 (不对外暴露)
│   ├── ui/               # 用户界面层
│   │   ├── ui_manager.go # UI管理器
│   │   ├── diary_ui.go   # 日记界面
│   │   └── anniversary_ui.go # 纪念日界面
│   ├── service/          # 业务逻辑层
│   │   ├── interfaces.go # 服务接口定义
│   │   ├── diary_service.go # 日记服务
│   │   └── config_service.go # 配置服务
│   ├── storage/          # 数据存储层
│   │   ├── interfaces.go # 存储接口定义
│   │   └── local_storage.go # 本地存储实现
│   └── model/            # 数据模型
│       └── models.go     # 数据结构定义
├── assets/               # 静态资源 (图标、图片等)
├── data/                 # 本地数据目录 (运行时创建)
├── config/               # 配置文件
│   └── app.json         # 应用配置
├── docs/                 # 文档目录
│   ├── 开发文档.md       # 开发文档
│   ├── 进度文档.md       # 进度跟踪
│   └── 快速启动指南.md   # 本文件
├── scripts/              # 构建脚本
│   ├── build.bat        # 构建脚本
│   └── run.bat          # 运行脚本
├── go.mod               # Go模块定义
├── go.sum               # 依赖版本锁定
└── README.md            # 项目说明
```

## 🎨 功能模块

### 当前可用功能
- ✅ **基础界面框架** - 主窗口、导航菜单、状态栏
- ✅ **日记模块基础** - 日记列表、编辑器界面
- ✅ **纪念日模块基础** - 纪念日列表、倒计时显示
- ✅ **配置管理** - 应用配置的保存和读取
- ✅ **本地存储** - JSON文件存储方案

### 开发中功能
- 🚧 **完整日记功能** - 图片上传、标签管理、搜索筛选
- 🚧 **完整纪念日功能** - 添加编辑、提醒通知
- 🚧 **照片收藏** - 相册管理、照片标签
- 🚧 **情话收藏** - 分类管理、随机显示

### 计划功能
- 📋 **统计分析** - 恋爱数据统计、图表展示
- 📋 **Redis存储** - 高性能数据库存储方案
- 📋 **数据备份** - 自动备份、恢复功能
- 📋 **主题切换** - 多种UI主题选择

## 🔧 开发指南

### 添加新功能
1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/storage/` 中添加存储接口和实现
3. 在 `internal/service/` 中实现业务逻辑
4. 在 `internal/ui/` 中创建用户界面
5. 在主程序中集成新功能

### 代码规范
- 遵循Go官方代码规范
- 使用有意义的变量和函数名
- 添加必要的注释和文档
- 进行充分的错误处理

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./internal/service

# 运行测试并显示覆盖率
go test -cover ./...
```

## 🐛 常见问题

### 1. 编译错误：CGO相关 ⚠️ 最常见问题
**问题**:
```
build constraints exclude all Go files in github.com/go-gl/gl
```
或 `gcc: command not found`

**原因**: Fyne GUI框架需要CGO支持，但系统缺少C编译器

**解决方案**:
1. **安装TDM-GCC (推荐)**
   - 下载：https://jmeubank.github.io/tdm-gcc/
   - 安装后重启命令行窗口

2. **使用环境修复脚本**
   ```bash
   scripts\fix_env.bat
   ```

3. **手动验证**
   ```bash
   gcc --version
   go env CGO_ENABLED  # 应该返回 "1"
   ```

### 2. 依赖下载失败
**问题**: 网络问题导致依赖下载失败
**解决**: 配置Go代理
```bash
go env -w GOPROXY=https://goproxy.cn,direct
```

### 3. 程序无法启动
**问题**: 双击exe文件没有反应
**解决**: 
- 检查是否有杀毒软件拦截
- 在命令行中运行查看错误信息
- 确保data目录有写入权限

### 4. 界面显示异常
**问题**: 界面元素显示不正常
**解决**:
- 检查屏幕DPI设置
- 尝试以管理员权限运行
- 更新显卡驱动

## 📞 获取帮助

如果遇到问题或需要帮助：

1. **查看文档** - 阅读 `docs/` 目录下的详细文档
2. **检查日志** - 查看程序运行时的控制台输出
3. **提交Issue** - 在项目仓库中提交问题报告
4. **联系开发者** - 通过邮件或其他方式联系开发团队

---

💝 **祝您使用愉快，记录每一个美好瞬间！**
