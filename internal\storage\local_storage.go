package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"love-software/internal/model"
)

// LocalStorage 本地文件存储实现
type LocalStorage struct {
	dataDir string
}

// NewLocalStorage 创建本地存储实例
func NewLocalStorage(dataDir string) (*LocalStorage, error) {
	storage := &LocalStorage{
		dataDir: dataDir,
	}
	
	if err := storage.Initialize(); err != nil {
		return nil, err
	}
	
	return storage, nil
}

// Initialize 初始化存储
func (s *LocalStorage) Initialize() error {
	// 创建数据目录
	dirs := []string{
		s.dataDir,
		filepath.Join(s.dataDir, "diary"),
		filepath.Join(s.dataDir, "albums"),
		filepath.Join(s.dataDir, "photos"),
		filepath.Join(s.dataDir, "backup"),
	}
	
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %w", dir, err)
		}
	}
	
	// 初始化配置文件
	configPath := filepath.Join(s.dataDir, "config.json")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := &model.UserConfig{
			Theme:         "pink",
			Language:      "zh-CN",
			StorageType:   "local",
			BackupEnabled: true,
			BackupInterval: 7,
			NotifyEnabled: true,
		}
		if err := s.SaveConfig(defaultConfig); err != nil {
			return fmt.Errorf("初始化配置失败: %w", err)
		}
	}
	
	return nil
}

// Close 关闭存储
func (s *LocalStorage) Close() error {
	// 本地文件存储无需特殊关闭操作
	return nil
}

// SaveDiaryEntry 保存日记条目
func (s *LocalStorage) SaveDiaryEntry(entry *model.DiaryEntry) error {
	// 按月份组织日记文件
	monthKey := entry.Date.Format("2006-01")
	filePath := filepath.Join(s.dataDir, "diary", fmt.Sprintf("%s.json", monthKey))
	
	// 读取现有数据
	var entries []*model.DiaryEntry
	if data, err := os.ReadFile(filePath); err == nil {
		json.Unmarshal(data, &entries)
	}
	
	// 添加新条目
	entries = append(entries, entry)
	
	// 保存到文件
	data, err := json.MarshalIndent(entries, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化日记数据失败: %w", err)
	}
	
	return os.WriteFile(filePath, data, 0644)
}

// GetDiaryEntry 获取日记条目
func (s *LocalStorage) GetDiaryEntry(id string) (*model.DiaryEntry, error) {
	// 遍历所有日记文件查找
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return nil, fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		for _, entry := range entries {
			if entry.ID == id {
				return entry, nil
			}
		}
	}
	
	return nil, fmt.Errorf("日记条目不存在: %s", id)
}

// GetDiaryEntries 获取日记条目列表
func (s *LocalStorage) GetDiaryEntries(filter *model.DiaryFilter) ([]*model.DiaryEntry, error) {
	var allEntries []*model.DiaryEntry
	
	// 读取所有日记文件
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return nil, fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		allEntries = append(allEntries, entries...)
	}
	
	// 应用筛选条件
	if filter != nil {
		allEntries = s.filterDiaryEntries(allEntries, filter)
	}
	
	return allEntries, nil
}

// filterDiaryEntries 筛选日记条目
func (s *LocalStorage) filterDiaryEntries(entries []*model.DiaryEntry, filter *model.DiaryFilter) []*model.DiaryEntry {
	var filtered []*model.DiaryEntry
	
	for _, entry := range entries {
		// 日期筛选
		if filter.StartDate != nil && entry.Date.Before(*filter.StartDate) {
			continue
		}
		if filter.EndDate != nil && entry.Date.After(*filter.EndDate) {
			continue
		}
		
		// 心情筛选
		if filter.Mood != nil && entry.Mood != *filter.Mood {
			continue
		}
		
		// 重要性筛选
		if filter.IsImportant != nil && entry.IsImportant != *filter.IsImportant {
			continue
		}
		
		// 关键词筛选
		if filter.Keyword != "" {
			found := false
			if contains(entry.Title, filter.Keyword) || contains(entry.Content, filter.Keyword) {
				found = true
			}
			for _, tag := range entry.Tags {
				if contains(tag, filter.Keyword) {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}
		
		// 标签筛选
		if len(filter.Tags) > 0 {
			found := false
			for _, filterTag := range filter.Tags {
				for _, entryTag := range entry.Tags {
					if entryTag == filterTag {
						found = true
						break
					}
				}
				if found {
					break
				}
			}
			if !found {
				continue
			}
		}
		
		filtered = append(filtered, entry)
	}
	
	return filtered
}

// UpdateDiaryEntry 更新日记条目
func (s *LocalStorage) UpdateDiaryEntry(entry *model.DiaryEntry) error {
	// 先删除旧条目，再保存新条目
	if err := s.DeleteDiaryEntry(entry.ID); err != nil {
		return err
	}
	
	entry.UpdatedAt = time.Now()
	return s.SaveDiaryEntry(entry)
}

// DeleteDiaryEntry 删除日记条目
func (s *LocalStorage) DeleteDiaryEntry(id string) error {
	// 遍历所有日记文件查找并删除
	diaryDir := filepath.Join(s.dataDir, "diary")
	files, err := os.ReadDir(diaryDir)
	if err != nil {
		return fmt.Errorf("读取日记目录失败: %w", err)
	}
	
	for _, file := range files {
		if filepath.Ext(file.Name()) != ".json" {
			continue
		}
		
		filePath := filepath.Join(diaryDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			continue
		}
		
		var entries []*model.DiaryEntry
		if err := json.Unmarshal(data, &entries); err != nil {
			continue
		}
		
		// 查找并删除条目
		for i, entry := range entries {
			if entry.ID == id {
				entries = append(entries[:i], entries[i+1:]...)
				
				// 保存更新后的数据
				newData, err := json.MarshalIndent(entries, "", "  ")
				if err != nil {
					return fmt.Errorf("序列化数据失败: %w", err)
				}
				
				return os.WriteFile(filePath, newData, 0644)
			}
		}
	}
	
	return fmt.Errorf("日记条目不存在: %s", id)
}

// SaveConfig 保存配置
func (s *LocalStorage) SaveConfig(config *model.UserConfig) error {
	configPath := filepath.Join(s.dataDir, "config.json")
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}
	
	return os.WriteFile(configPath, data, 0644)
}

// GetConfig 获取配置
func (s *LocalStorage) GetConfig() (*model.UserConfig, error) {
	configPath := filepath.Join(s.dataDir, "config.json")
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	
	var config model.UserConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}
	
	return &config, nil
}

// contains 检查字符串是否包含子字符串（忽略大小写）
func contains(s, substr string) bool {
	// 简单实现，实际应用中可以使用更复杂的搜索算法
	return len(s) >= len(substr) && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      containsMiddle(s, substr))))
}

func containsMiddle(s, substr string) bool {
	for i := 1; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// 其他方法的实现将在后续添加...
