@echo off
echo 💕 爱情个人软件 - 运行脚本
echo ================================

:: 检查Go环境
echo 🔍 检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到Go环境，请先安装Go
    pause
    exit /b 1
)

:: 检查和设置CGO
echo 🔍 检查CGO环境...
for /f "tokens=*" %%i in ('go env CGO_ENABLED') do set CGO_STATUS=%%i
if "%CGO_STATUS%"=="0" (
    echo 🔧 启用CGO...
    set CGO_ENABLED=1
    go env -w CGO_ENABLED=1
)

:: 检查GCC编译器
echo 🔍 检查GCC编译器...
gcc --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到GCC编译器
    echo 💡 解决方案:
    echo    1. 安装TDM-GCC: https://jmeubank.github.io/tdm-gcc/
    echo    2. 或安装MSYS2: https://www.msys2.org/
    echo    3. 安装后重启命令行窗口
    echo.
    echo 📖 详细说明请查看: docs\环境配置指南.md
    pause
    exit /b 1
)

echo ✅ 环境检查通过！

:: 设置环境变量
set CGO_ENABLED=1

echo 🚀 正在启动应用程序...

:: 安装依赖
echo 📦 检查依赖...
go mod tidy

if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ 依赖安装可能有问题，尝试设置代理...
    go env -w GOPROXY=https://goproxy.cn,direct
    go mod tidy
)

:: 运行程序
echo 🎯 启动爱情个人软件...
go run cmd/main.go

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 程序启动失败！
    echo 💡 可能的解决方案:
    echo    1. 检查是否安装了GCC编译器
    echo    2. 重启命令行窗口
    echo    3. 查看详细错误信息
    echo.
    echo 📖 详细说明请查看: docs\环境配置指南.md
)

pause
