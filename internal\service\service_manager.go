package service

import (
	"love-software/internal/storage"
)

// ServiceManager 服务管理器
type ServiceManager struct {
	DiaryService       DiaryService
	AnniversaryService AnniversaryService
	PhotoService       PhotoService
	QuoteService       QuoteService
	ConfigService      ConfigService
	StatisticsService  StatisticsService
	NotificationService NotificationService
	BackupService      BackupService
}

// NewServiceManager 创建服务管理器
func NewServiceManager(storage storage.Storage) *ServiceManager {
	return &ServiceManager{
		DiaryService:       NewDiaryService(storage),
		AnniversaryService: NewAnniversaryService(storage),
		PhotoService:       NewPhotoService(storage),
		QuoteService:       NewQuoteService(storage),
		ConfigService:      NewConfigService(storage),
		StatisticsService:  NewStatisticsService(storage),
		NotificationService: NewNotificationService(storage),
		BackupService:      NewBackupService(storage),
	}
}
